#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗
██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║
██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║
██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║
██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║

BAGLAN SEARCH v2.0 - Advanced Search & OSINT Tool
Created by: @nerwa_degme
Company: BAGLAN Tech Solutions
"""

# IMPORT MODULES
import json
import requests
import time
import os
import sys
import phonenumbers
from phonenumbers import carrier, geocoder, timezone

# BAGLAN Color Scheme - Cyan/Turquoise Gradient (görüntünüzdeki gibi)
class Colors:
    # Ana renk paleti
    CYAN_BRIGHT = '\033[38;2;0;255;255m'     # Parlak cyan
    TURQUOISE = '\033[38;2;0;255;204m'       # Turkuaz
    GREEN_CYAN = '\033[38;2;0;255;179m'      # Yeşilimsi cyan
    DARK_CYAN = '\033[38;2;0;200;200m'       # Koyu cyan
    
    # Yardımcı renkler
    GREEN = '\033[38;2;100;255;100m'         # Yeşil
    RED = '\033[38;2;255;100;100m'           # Kırmızı
    YELLOW = '\033[38;2;255;255;100m'        # Sarı
    WHITE = '\033[38;2;255;255;255m'         # Beyaz
    
    # Stil
    BOLD = '\033[1m'
    DIM = '\033[2m'
    RESET = '\033[0m'

# Kısa erişim için
C = Colors()

class BaglanSearch:
    def __init__(self):
        self.version = "2.0"
        self.author = "@nerwa_degme"
        self.company = "BAGLAN Tech Solutions"
        
    def clear_screen(self):
        """Ekranı temizle"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def animate_logo(self):
        """BAGLAN SEARCH süper animasyonlu logo"""

        # Başlangıç animasyonu - harfler tek tek belirir
        self.clear_screen()
        print(f"\n{C.CYAN_BRIGHT}{C.BOLD}")
        print("    ╔══════════════════════════════════════════════════════════╗")
        print("    ║                                                          ║")

        # BAGLAN harflerini tek tek göster
        baglan_letters = [
            "     ██████╗  ",
            "█████╗  ",
            "██████╗ ",
            "██╗      ",
            "█████╗ ",
            "███╗   ██╗"
        ]

        line2 = "    ║     "
        for i, letter in enumerate(baglan_letters):
            line2 += letter
            print(f"\r{line2}{' ' * (45 - len(line2))}║", end='', flush=True)
            time.sleep(0.3)

        print()
        time.sleep(0.5)

        # Tam logo frame'leri
        logo_frames = [
            # Frame 1 - BAGLAN
            f"""{C.CYAN_BRIGHT}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}""",

            # Frame 2 - BAGLAN + SEARCH (kısmen)
            f"""{C.TURQUOISE}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ║           ███████╗███████╗ █████╗ ██████╗  ██████╗      ║
    ║           ██╔════╝██╔════╝██╔══██╗██╔══██╗██╔════╝      ║
    ║           ███████╗█████╗  ███████║██████╔╝██║           ║
    ║           ╚════██║██╔══╝  ██╔══██║██╔══██╗██║           ║
    ║           ███████║███████╗██║  ██║██║  ██║╚██████╗      ║
    ║           ╚══════╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝      ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}""",

            # Frame 3 - BAGLAN SEARCH + İkonlar
            f"""{C.GREEN_CYAN}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ║           ███████╗███████╗ █████╗ ██████╗  ██████╗      ║
    ║           ██╔════╝██╔════╝██╔══██╗██╔══██╗██╔════╝      ║
    ║           ███████╗█████╗  ███████║██████╔╝██║           ║
    ║           ╚════██║██╔══╝  ██╔══██║██╔══██╗██║           ║
    ║           ███████║███████╗██║  ██║██║  ██║╚██████╗      ║
    ║           ╚══════╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝      ║
    ║                                                          ║
    ║              {C.CYAN_BRIGHT}🔍 ADVANCED SEARCH ENGINE 🔍{C.GREEN_CYAN}              ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}""",

            # Frame 4 - Final with effects
            f"""{C.CYAN_BRIGHT}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║  {C.TURQUOISE}✨{C.CYAN_BRIGHT}                                                    {C.TURQUOISE}✨{C.CYAN_BRIGHT}  ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║  {C.GREEN_CYAN}🔍{C.CYAN_BRIGHT}                                                    {C.GREEN_CYAN}🔍{C.CYAN_BRIGHT}  ║
    ║           ███████╗███████╗ █████╗ ██████╗  ██████╗      ║
    ║           ██╔════╝██╔════╝██╔══██╗██╔══██╗██╔════╝      ║
    ║           ███████╗█████╗  ███████║██████╔╝██║           ║
    ║           ╚════██║██╔══╝  ██╔══██║██╔══██╗██║           ║
    ║           ███████║███████╗██║  ██║██║  ██║╚██████╗      ║
    ║           ╚══════╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝      ║
    ║  {C.TURQUOISE}⚡{C.CYAN_BRIGHT}                                                    {C.TURQUOISE}⚡{C.CYAN_BRIGHT}  ║
    ║              {C.GREEN_CYAN}🔍 ADVANCED SEARCH ENGINE 🔍{C.CYAN_BRIGHT}              ║
    ║  {C.GREEN_CYAN}✨{C.CYAN_BRIGHT}                                                    {C.GREEN_CYAN}✨{C.CYAN_BRIGHT}  ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}"""
        ]

        # Animasyon gösterimi - her frame daha uzun
        for i, frame in enumerate(logo_frames):
            self.clear_screen()
            print(frame)

            # Son frame'de ekstra efektler
            if i == len(logo_frames) - 1:
                time.sleep(0.5)
                # Yanıp sönen efekt
                for _ in range(3):
                    self.clear_screen()
                    print(frame.replace('✨', '💫').replace('⚡', '🌟'))
                    time.sleep(0.2)
                    self.clear_screen()
                    print(frame)
                    time.sleep(0.2)
            else:
                time.sleep(1.0)

        # Süper animasyonlu başlık
        self.show_animated_title()

        time.sleep(1)

    def show_animated_title(self):
        """Süper animasyonlu başlık gösterimi"""
        print(f"\n{C.TURQUOISE}{C.BOLD}")

        # Çizgi animasyonu
        line = ""
        for i in range(62):
            line += "═"
            print(f"\r{line}", end='', flush=True)
            time.sleep(0.02)

        print()

        # Başlık animasyonu - harf harf
        title = "🚀 BAGLAN SEARCH v2.0 🚀"
        spaces = (62 - len(title)) // 2
        animated_title = " " * spaces

        for char in title:
            animated_title += char
            print(f"\r{animated_title}", end='', flush=True)
            time.sleep(0.1)

        print()

        # Alt çizgi animasyonu
        line = ""
        for i in range(62):
            line += "═"
            print(f"\r{line}", end='', flush=True)
            time.sleep(0.02)

        print()

        # Alt başlıklar animasyonu
        subtitles = [
            "⚡ Advanced Search Engine ⚡",
            f"👤 Created by {self.author}",
            f"🏢 {self.company}"
        ]

        for subtitle in subtitles:
            spaces = (62 - len(subtitle)) // 2
            animated_subtitle = " " * spaces

            for char in subtitle:
                animated_subtitle += char
                print(f"\r{C.GREEN_CYAN}{animated_subtitle}", end='', flush=True)
                time.sleep(0.05)

            print()
            time.sleep(0.3)

        # Final çizgi
        print(f"{C.TURQUOISE}")
        line = ""
        for i in range(62):
            line += "═"
            print(f"\r{line}", end='', flush=True)
            time.sleep(0.02)

        print(f"{C.RESET}")

    def show_loading(self, text="Yükleniyor", duration=20):
        """Gelişmiş animasyonlu yükleme göstergesi"""
        # Çoklu spinner karakterleri
        spinners = [
            ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'],
            ['◐', '◓', '◑', '◒'],
            ['▁', '▃', '▄', '▅', '▆', '▇', '█', '▇', '▆', '▅', '▄', '▃'],
            ['🔍', '🔎', '🔍', '🔎']
        ]

        # Rastgele spinner seç
        import random
        spinner = random.choice(spinners)

        for i in range(duration):
            char = spinner[i % len(spinner)]

            # Progress bar
            progress = int((i / duration) * 20)
            bar = '█' * progress + '░' * (20 - progress)
            percentage = int((i / duration) * 100)

            print(f"\r{C.CYAN_BRIGHT}{char} {text}... [{C.TURQUOISE}{bar}{C.CYAN_BRIGHT}] {percentage}%", end='', flush=True)
            time.sleep(0.1)

        print(f"\r{C.GREEN}✅ {text} tamamlandı! {' ' * 30}{C.RESET}")
        time.sleep(0.5)
    
    def show_loading(self, text="Yükleniyor"):
        """Animasyonlu yükleme göstergesi"""
        spinner = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        for i in range(20):
            char = spinner[i % len(spinner)]
            print(f"\r{C.CYAN_BRIGHT}{char} {text}... {C.RESET}", end='', flush=True)
            time.sleep(0.1)
        print(f"\r{C.GREEN}✅ {text} tamamlandı!{' ' * 20}{C.RESET}")
        time.sleep(0.5)
    
    def show_menu(self):
        """Süper animasyonlu menü"""
        print(f"\n{C.TURQUOISE}{C.BOLD}")

        # Menü başlığı animasyonu
        title = "🎯 BAGLAN SEARCH MENÜ"
        for i, char in enumerate(title):
            print(f"\r{title[:i+1]}", end='', flush=True)
            time.sleep(0.05)

        print()

        # Çizgi animasyonu
        line = ""
        for i in range(40):
            line += "─"
            print(f"\r{C.CYAN_BRIGHT}{line}", end='', flush=True)
            time.sleep(0.03)

        print()

        # Menü öğeleri animasyonu
        menu_items = [
            "[ 1 ] 🌐 IP Address Search",
            "[ 2 ] 📱 Phone Number Search",
            "[ 3 ] 👤 Username Search",
            "[ 4 ] 🔍 Show Your IP",
            "[ 0 ] ❌ Exit"
        ]

        for item in menu_items:
            # Her menü öğesi için animasyon
            animated_item = ""
            color = C.GREEN_CYAN if not item.startswith("[ 0 ]") else C.RED

            for char in item:
                animated_item += char
                print(f"\r{color}{animated_item}", end='', flush=True)
                time.sleep(0.03)

            print()
            time.sleep(0.2)

        # Alt çizgi animasyonu
        line = ""
        for i in range(40):
            line += "─"
            print(f"\r{C.CYAN_BRIGHT}{line}", end='', flush=True)
            time.sleep(0.03)

        print()

        # Seçim animasyonu
        prompt = "🎯 Seçiminizi yapın: "
        animated_prompt = ""

        for char in prompt:
            animated_prompt += char
            print(f"\r{C.TURQUOISE}{animated_prompt}", end='', flush=True)
            time.sleep(0.05)

        choice = input(f"{C.WHITE}")
        return choice
    
    def ip_tracker(self):
        """IP adresi takip et"""
        self.clear_screen()
        self.show_header("🌐 IP ADDRESS TRACKER")
        
        ip = input(f"{C.CYAN_BRIGHT}📡 Hedef IP adresini girin: {C.WHITE}")
        
        if not ip:
            print(f"{C.RED}❌ IP adresi boş olamaz!{C.RESET}")
            return
        
        self.show_loading("IP bilgileri alınıyor")
        
        try:
            print(f"\n{C.TURQUOISE}{C.BOLD}{'═' * 50}")
            print(f"{C.TURQUOISE}🔍 IP ADRESİ BİLGİLERİ")
            print(f"{C.TURQUOISE}{'═' * 50}")
            
            # API çağrısı
            response = requests.get(f"http://ipwho.is/{ip}", timeout=10)
            data = response.json()
            
            if data.get('success', True):
                # Bilgileri göster
                print(f"{C.CYAN_BRIGHT}📡 IP Adresi      : {C.WHITE}{ip}")
                print(f"{C.CYAN_BRIGHT}🌐 Tip            : {C.WHITE}{data.get('type', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🏳️  Ülke          : {C.WHITE}{data.get('country', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🏙️  Şehir         : {C.WHITE}{data.get('city', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🗺️  Bölge         : {C.WHITE}{data.get('region', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🌍 Kıta          : {C.WHITE}{data.get('continent', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}📍 Enlem         : {C.WHITE}{data.get('latitude', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}📍 Boylam        : {C.WHITE}{data.get('longitude', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🏢 ISP           : {C.WHITE}{data.get('isp', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🏢 Organizasyon  : {C.WHITE}{data.get('org', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}📮 Posta Kodu    : {C.WHITE}{data.get('postal', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🕐 Zaman Dilimi  : {C.WHITE}{data.get('timezone', {}).get('id', 'N/A')}")
                
                # Harita linki
                if data.get('latitude') and data.get('longitude'):
                    lat, lon = data['latitude'], data['longitude']
                    maps_url = f"https://www.google.com/maps/@{lat},{lon},12z"
                    print(f"{C.GREEN}🗺️  Google Maps   : {C.WHITE}{maps_url}")
                
                print(f"{C.TURQUOISE}{'═' * 50}{C.RESET}")
                
            else:
                print(f"{C.RED}❌ IP adresi bilgileri alınamadı!{C.RESET}")
                
        except requests.exceptions.RequestException as e:
            print(f"{C.RED}❌ Bağlantı hatası: {e}{C.RESET}")
        except Exception as e:
            print(f"{C.RED}❌ Hata oluştu: {e}{C.RESET}")
        
        input(f"\n{C.TURQUOISE}📱 Devam etmek için Enter'a basın...{C.RESET}")

    def phone_tracker(self):
        """Telefon numarası takip et"""
        self.clear_screen()
        self.show_header("📱 PHONE NUMBER TRACKER")

        phone = input(f"{C.CYAN_BRIGHT}📞 Telefon numarasını girin (örn: +905551234567): {C.WHITE}")

        if not phone:
            print(f"{C.RED}❌ Telefon numarası boş olamaz!{C.RESET}")
            return

        self.show_loading("Telefon numarası analiz ediliyor")

        try:
            print(f"\n{C.TURQUOISE}{C.BOLD}{'═' * 50}")
            print(f"{C.TURQUOISE}📱 TELEFON NUMARASI BİLGİLERİ")
            print(f"{C.TURQUOISE}{'═' * 50}")

            # Telefon numarasını parse et
            parsed_number = phonenumbers.parse(phone, None)

            # Geçerlilik kontrolü
            if phonenumbers.is_valid_number(parsed_number):
                # Bilgileri al
                country = geocoder.description_for_number(parsed_number, "tr")
                carrier_name = carrier.name_for_number(parsed_number, "tr")
                timezones = timezone.time_zones_for_number(parsed_number)

                # Ülke kodu
                country_code = parsed_number.country_code
                national_number = parsed_number.national_number

                # Numarayı formatla
                international_format = phonenumbers.format_number(parsed_number, phonenumbers.PhoneNumberFormat.INTERNATIONAL)
                national_format = phonenumbers.format_number(parsed_number, phonenumbers.PhoneNumberFormat.NATIONAL)
                e164_format = phonenumbers.format_number(parsed_number, phonenumbers.PhoneNumberFormat.E164)

                # Bilgileri göster
                print(f"{C.CYAN_BRIGHT}📞 Orijinal Numara    : {C.WHITE}{phone}")
                print(f"{C.CYAN_BRIGHT}🌐 Uluslararası Format: {C.WHITE}{international_format}")
                print(f"{C.CYAN_BRIGHT}🏠 Ulusal Format      : {C.WHITE}{national_format}")
                print(f"{C.CYAN_BRIGHT}📋 E164 Format        : {C.WHITE}{e164_format}")
                print(f"{C.CYAN_BRIGHT}🏳️  Ülke Kodu         : {C.WHITE}+{country_code}")
                print(f"{C.CYAN_BRIGHT}🏙️  Ülke/Bölge        : {C.WHITE}{country if country else 'Bilinmiyor'}")
                print(f"{C.CYAN_BRIGHT}📡 Operatör           : {C.WHITE}{carrier_name if carrier_name else 'Bilinmiyor'}")
                print(f"{C.CYAN_BRIGHT}🕐 Zaman Dilimleri    : {C.WHITE}{', '.join(timezones) if timezones else 'Bilinmiyor'}")

                # Numara tipi
                number_type = phonenumbers.number_type(parsed_number)
                type_names = {
                    phonenumbers.PhoneNumberType.MOBILE: "📱 Mobil",
                    phonenumbers.PhoneNumberType.FIXED_LINE: "🏠 Sabit Hat",
                    phonenumbers.PhoneNumberType.FIXED_LINE_OR_MOBILE: "📱🏠 Sabit/Mobil",
                    phonenumbers.PhoneNumberType.TOLL_FREE: "🆓 Ücretsiz",
                    phonenumbers.PhoneNumberType.PREMIUM_RATE: "💰 Ücretli",
                    phonenumbers.PhoneNumberType.VOIP: "💻 VoIP",
                    phonenumbers.PhoneNumberType.UNKNOWN: "❓ Bilinmiyor"
                }
                print(f"{C.CYAN_BRIGHT}📋 Numara Tipi        : {C.WHITE}{type_names.get(number_type, '❓ Bilinmiyor')}")

                # Geçerlilik durumu
                print(f"{C.GREEN}✅ Numara Geçerli     : {C.WHITE}Evet")

                print(f"{C.TURQUOISE}{'═' * 50}{C.RESET}")

            else:
                print(f"{C.RED}❌ Geçersiz telefon numarası!{C.RESET}")

        except phonenumbers.phonenumberutil.NumberParseException as e:
            print(f"{C.RED}❌ Numara parse hatası: {e}{C.RESET}")
        except Exception as e:
            print(f"{C.RED}❌ Hata oluştu: {e}{C.RESET}")

        input(f"\n{C.TURQUOISE}📱 Devam etmek için Enter'a basın...{C.RESET}")

    def username_tracker(self):
        """Kullanıcı adı takip et"""
        self.clear_screen()
        self.show_header("👤 USERNAME TRACKER")

        username = input(f"{C.CYAN_BRIGHT}👤 Kullanıcı adını girin: {C.WHITE}")

        if not username:
            print(f"{C.RED}❌ Kullanıcı adı boş olamaz!{C.RESET}")
            return

        self.show_loading("Sosyal medya platformları taranıyor")

        # Sosyal medya platformları
        platforms = {
            "Instagram": f"https://www.instagram.com/{username}/",
            "Twitter": f"https://twitter.com/{username}",
            "GitHub": f"https://github.com/{username}",
            "YouTube": f"https://www.youtube.com/@{username}",
            "TikTok": f"https://www.tiktok.com/@{username}",
            "LinkedIn": f"https://www.linkedin.com/in/{username}",
            "Facebook": f"https://www.facebook.com/{username}",
            "Reddit": f"https://www.reddit.com/user/{username}",
            "Pinterest": f"https://www.pinterest.com/{username}",
            "Telegram": f"https://t.me/{username}"
        }

        print(f"\n{C.TURQUOISE}{C.BOLD}{'═' * 50}")
        print(f"{C.TURQUOISE}👤 KULLANICI ADI ARAŞTIRMASI")
        print(f"{C.TURQUOISE}{'═' * 50}")
        print(f"{C.CYAN_BRIGHT}🔍 Aranan Kullanıcı: {C.WHITE}{username}")
        print(f"{C.TURQUOISE}{'─' * 50}")

        # Platform linklerini göster
        for platform, url in platforms.items():
            print(f"{C.GREEN_CYAN}📱 {platform:<12}: {C.WHITE}{url}")
            time.sleep(0.1)  # Animasyon efekti

        print(f"{C.TURQUOISE}{'═' * 50}")
        print(f"{C.YELLOW}💡 Not: Bu linkler otomatik oluşturulmuştur.")
        print(f"{C.YELLOW}   Profillerin varlığını kontrol etmek için linkleri ziyaret edin.{C.RESET}")

        input(f"\n{C.TURQUOISE}📱 Devam etmek için Enter'a basın...{C.RESET}")

    def show_my_ip(self):
        """Kendi IP adresini göster"""
        self.clear_screen()
        self.show_header("🔍 YOUR IP ADDRESS")

        self.show_loading("IP adresiniz alınıyor")

        try:
            print(f"\n{C.TURQUOISE}{C.BOLD}{'═' * 50}")
            print(f"{C.TURQUOISE}🔍 SİZİN IP ADRESİNİZ")
            print(f"{C.TURQUOISE}{'═' * 50}")

            # Kendi IP'yi al
            response = requests.get("http://ipwho.is/", timeout=10)
            data = response.json()

            if data.get('success', True):
                print(f"{C.CYAN_BRIGHT}📡 IP Adresiniz   : {C.WHITE}{data.get('ip', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🌐 Tip            : {C.WHITE}{data.get('type', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🏳️  Ülke          : {C.WHITE}{data.get('country', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🏙️  Şehir         : {C.WHITE}{data.get('city', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🗺️  Bölge         : {C.WHITE}{data.get('region', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🏢 ISP           : {C.WHITE}{data.get('isp', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🏢 Organizasyon  : {C.WHITE}{data.get('org', 'N/A')}")
                print(f"{C.CYAN_BRIGHT}🕐 Zaman Dilimi  : {C.WHITE}{data.get('timezone', {}).get('id', 'N/A')}")

                print(f"{C.TURQUOISE}{'═' * 50}{C.RESET}")

            else:
                print(f"{C.RED}❌ IP adresi bilgileri alınamadı!{C.RESET}")

        except requests.exceptions.RequestException as e:
            print(f"{C.RED}❌ Bağlantı hatası: {e}{C.RESET}")
        except Exception as e:
            print(f"{C.RED}❌ Hata oluştu: {e}{C.RESET}")

        input(f"\n{C.TURQUOISE}📱 Devam etmek için Enter'a basın...{C.RESET}")

    def show_header(self, title):
        """Başlık göster"""
        print(f"{C.TURQUOISE}{C.BOLD}")
        print("╔" + "═" * 58 + "╗")
        print(f"║{title:^58}║")
        print("╚" + "═" * 58 + "╝")
        print(f"{C.RESET}")
    
    def run(self):
        """Ana program döngüsü"""
        self.animate_logo()
        
        while True:
            try:
                choice = self.show_menu()
                
                if choice == '1':
                    self.ip_tracker()
                elif choice == '2':
                    self.phone_tracker()
                elif choice == '3':
                    self.username_tracker()
                elif choice == '4':
                    self.show_my_ip()
                elif choice == '0':
                    print(f"{C.TURQUOISE}👋 BAGLAN Ghost Tracker'dan çıkılıyor...")
                    print(f"{C.CYAN_BRIGHT}🙏 Teşekkürler! - {self.author}{C.RESET}")
                    break
                else:
                    print(f"{C.RED}❌ Geçersiz seçim! Lütfen 0-4 arası bir sayı girin.{C.RESET}")
                    time.sleep(1)
                    
            except KeyboardInterrupt:
                print(f"\n{C.RED}❌ Program kullanıcı tarafından durduruldu.{C.RESET}")
                break
            except Exception as e:
                print(f"{C.RED}❌ Beklenmeyen hata: {e}{C.RESET}")
                time.sleep(2)

if __name__ == "__main__":
    search = BaglanSearch()
    search.run()
