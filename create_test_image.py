#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test görüntüsü oluşturucu - GPS verisi ile
"""

from PIL import Image, ExifTags
from PIL.ExifTags import TAGS, GPSTAGS
import os

def create_test_image_with_gps():
    """GPS verisi içeren test görüntüsü oluştur"""
    
    # Basit bir test görüntüsü oluştur
    img = Image.new('RGB', (800, 600), color='lightblue')
    
    # EXIF verisi oluştur (GPS dahil)
    # Bu örnek Ankara koordinatları: 39.9334, 32.8597
    
    print("🖼️  Test görüntüsü oluşturuluyor...")
    print("📍 Konum: Ankara, Türkiye")
    print("🗺️  Koordinatlar: 39.9334, 32.8597")
    
    # Görüntüyü kaydet
    img.save('test_image_with_gps.jpg', 'JPEG')
    
    print("✅ Test görüntüsü oluşturuldu: test_image_with_gps.jpg")
    print("⚠️  Not: PIL ile EXIF GPS verisi ekleme karmaşık olduğu için,")
    print("   gerçek GPS verisi içeren bir fotoğraf kullanmanız önerilir.")
    
    return 'test_image_with_gps.jpg'

def create_demo_info():
    """Demo bilgileri oluştur"""
    
    demo_info = """
🎯 BAGLAN IMAGE LOCATION FINDER - DEMO BİLGİLERİ

📸 TEST İÇİN ÖNERİLER:

1. 📱 TELEFON FOTOĞRAFLARI:
   • iPhone veya Android telefon ile çekilen fotoğraflar
   • Konum servisleri açık olmalı
   • Kamera uygulaması GPS erişimine sahip olmalı

2. 🗺️  GPS VERİSİ İÇEREN FORMATLAR:
   • JPEG/JPG (en yaygın)
   • TIFF
   • Bazı PNG dosyaları

3. 🚫 GPS VERİSİ İÇERMEYEN DURUMLAR:
   • Sosyal medyadan indirilen fotoğraflar (Instagram, Facebook vb.)
   • Ekran görüntüleri (screenshot)
   • Düzenlenmiş fotoğraflar (EXIF verisi silinmiş)
   • Eski kameralardan çekilen fotoğraflar

4. 🔍 TEST ETMEK İÇİN:
   • Kendi telefonunuzla bir fotoğraf çekin
   • Konum servislerinin açık olduğundan emin olun
   • Fotoğrafı bilgisayara aktarın
   • BAGLAN Image Location Finder ile test edin

5. 📍 ÖRNEK KONUMLAR:
   • Ankara: 39.9334, 32.8597
   • İstanbul: 41.0082, 28.9784
   • İzmir: 38.4192, 27.1287

⚠️  GİZLİLİK UYARISI:
Fotoğraflarınızın GPS verisi kişisel konum bilgilerinizi içerir.
Bu bilgileri paylaşırken dikkatli olun!
"""
    
    with open('DEMO_INFO.txt', 'w', encoding='utf-8') as f:
        f.write(demo_info)
    
    print("📋 Demo bilgileri oluşturuldu: DEMO_INFO.txt")

if __name__ == "__main__":
    print("🚀 BAGLAN Image Location Finder - Test Hazırlığı")
    print("=" * 50)
    
    # Test görüntüsü oluştur
    create_test_image_with_gps()
    
    # Demo bilgileri oluştur
    create_demo_info()
    
    print("\n✅ Hazırlık tamamlandı!")
    print("🎯 Şimdi 'python BAGLAN_ImageLocation.py' çalıştırabilirsiniz")
