#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
BAGLAN GHOST TRACKER - Demo Sürümü
Created by: @nerwa_degme
Company: BAGLAN Tech Solutions
"""

import time
import os

# BAGLAN Color Scheme
class Colors:
    CYAN_BRIGHT = '\033[38;2;0;255;255m'     # Parlak cyan
    TURQUOISE = '\033[38;2;0;255;204m'       # Turkuaz
    GREEN_CYAN = '\033[38;2;0;255;179m'      # Yeşilimsi cyan
    GREEN = '\033[38;2;100;255;100m'         # Yeşil
    RED = '\033[38;2;255;100;100m'           # Kırmızı
    YELLOW = '\033[38;2;255;255;100m'        # Sarı
    WHITE = '\033[38;2;255;255;255m'         # Beyaz
    BOLD = '\033[1m'
    RESET = '\033[0m'

C = Colors()

class BaglanGhostDemo:
    def __init__(self):
        self.version = "2.0"
        self.author = "@nerwa_degme"
        self.company = "BAGLAN Tech Solutions"
    
    def clear_screen(self):
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def animate_logo(self):
        """BAGLAN animasyonlu logo"""
        logo_frames = [
            f"""{C.CYAN_BRIGHT}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}""",
            
            f"""{C.TURQUOISE}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ║          ██████╗ ██╗  ██╗ ██████╗ ███████╗████████╗      ║
    ║         ██╔════╝ ██║  ██║██╔═══██╗██╔════╝╚══██╔══╝      ║
    ║         ██║  ███╗███████║██║   ██║███████╗   ██║         ║
    ║         ██║   ██║██╔══██║██║   ██║╚════██║   ██║         ║
    ║         ╚██████╔╝██║  ██║╚██████╔╝███████║   ██║         ║
    ║          ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝   ╚═╝         ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}""",
            
            f"""{C.GREEN_CYAN}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ║          ██████╗ ██╗  ██╗ ██████╗ ███████╗████████╗      ║
    ║         ██╔════╝ ██║  ██║██╔═══██╗██╔════╝╚══██╔══╝      ║
    ║         ██║  ███╗███████║██║   ██║███████╗   ██║         ║
    ║         ██║   ██║██╔══██║██║   ██║╚════██║   ██║         ║
    ║         ╚██████╔╝██║  ██║╚██████╔╝███████║   ██║         ║
    ║          ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝   ╚═╝         ║
    ║                                                          ║
    ║              {C.CYAN_BRIGHT}🔍 TRACKER - ADVANCED OSINT 🔍{C.GREEN_CYAN}              ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}"""
        ]
        
        for frame in logo_frames:
            self.clear_screen()
            print(frame)
            time.sleep(0.8)
        
        print(f"\n{C.TURQUOISE}{C.BOLD}{'═' * 62}")
        print(f"{C.TURQUOISE}           🚀 BAGLAN GHOST TRACKER v{self.version} 🚀")
        print(f"{C.TURQUOISE}{'═' * 62}")
        print(f"{C.GREEN_CYAN}              ⚡ Advanced OSINT Tool ⚡")
        print(f"{C.CYAN_BRIGHT}               👤 Created by {self.author}")
        print(f"{C.TURQUOISE}              🏢 {self.company}")
        print(f"{C.TURQUOISE}{'═' * 62}{C.RESET}")
        time.sleep(1)
    
    def demo_ip_tracker(self):
        """IP Tracker demosu"""
        print(f"\n{C.TURQUOISE}{C.BOLD}🌐 IP TRACKER DEMO")
        print(f"{C.CYAN_BRIGHT}{'─' * 40}")
        
        demo_ips = ["*******", "*******", "**************"]
        
        for ip in demo_ips:
            print(f"{C.GREEN_CYAN}📡 Demo IP: {C.WHITE}{ip}")
            
            # Animasyonlu yükleme
            spinner = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
            for i in range(10):
                char = spinner[i % len(spinner)]
                print(f"\r{C.CYAN_BRIGHT}{char} IP bilgileri alınıyor...", end='', flush=True)
                time.sleep(0.1)
            
            print(f"\r{C.GREEN}✅ IP analizi tamamlandı!{' ' * 20}")
            print(f"{C.CYAN_BRIGHT}🏳️  Ülke: {C.WHITE}{'USA' if ip == '*******' else 'Demo Ülke'}")
            print(f"{C.CYAN_BRIGHT}🏙️  Şehir: {C.WHITE}{'Mountain View' if ip == '*******' else 'Demo Şehir'}")
            print(f"{C.CYAN_BRIGHT}🏢 ISP: {C.WHITE}{'Google LLC' if ip == '*******' else 'Demo ISP'}")
            print(f"{C.TURQUOISE}{'─' * 40}")
            time.sleep(1)
    
    def demo_phone_tracker(self):
        """Phone Tracker demosu"""
        print(f"\n{C.TURQUOISE}{C.BOLD}📱 PHONE TRACKER DEMO")
        print(f"{C.CYAN_BRIGHT}{'─' * 40}")
        
        demo_phones = ["+905551234567", "+12125551234", "+447700900123"]
        
        for phone in demo_phones:
            print(f"{C.GREEN_CYAN}📞 Demo Telefon: {C.WHITE}{phone}")
            
            # Animasyonlu analiz
            for i in range(8):
                char = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧'][i % 8]
                print(f"\r{C.CYAN_BRIGHT}{char} Telefon analiz ediliyor...", end='', flush=True)
                time.sleep(0.1)
            
            print(f"\r{C.GREEN}✅ Telefon analizi tamamlandı!{' ' * 25}")
            
            if phone.startswith("+90"):
                print(f"{C.CYAN_BRIGHT}🏳️  Ülke: {C.WHITE}Türkiye")
                print(f"{C.CYAN_BRIGHT}📱 Tip: {C.WHITE}Mobil")
                print(f"{C.CYAN_BRIGHT}📡 Operatör: {C.WHITE}Demo Operatör")
            else:
                print(f"{C.CYAN_BRIGHT}🏳️  Ülke: {C.WHITE}Demo Ülke")
                print(f"{C.CYAN_BRIGHT}📱 Tip: {C.WHITE}Demo Tip")
            
            print(f"{C.TURQUOISE}{'─' * 40}")
            time.sleep(1)
    
    def demo_username_tracker(self):
        """Username Tracker demosu"""
        print(f"\n{C.TURQUOISE}{C.BOLD}👤 USERNAME TRACKER DEMO")
        print(f"{C.CYAN_BRIGHT}{'─' * 40}")
        
        demo_username = self.author
        print(f"{C.GREEN_CYAN}👤 Demo Kullanıcı: {C.WHITE}{demo_username}")
        
        platforms = ["Instagram", "Twitter", "GitHub", "YouTube", "TikTok"]
        
        for platform in platforms:
            print(f"{C.CYAN_BRIGHT}📱 {platform:<12}: {C.WHITE}https://{platform.lower()}.com/{demo_username}")
            time.sleep(0.2)
        
        print(f"{C.TURQUOISE}{'─' * 40}")
        print(f"{C.YELLOW}💡 Sosyal medya profilleri otomatik oluşturuldu!")
    
    def demo_features(self):
        """Özellikler demosu"""
        print(f"\n{C.TURQUOISE}{C.BOLD}🚀 BAGLAN GHOST TRACKER ÖZELLİKLERİ")
        print(f"{C.CYAN_BRIGHT}{'═' * 50}")
        
        features = [
            "🌐 Gelişmiş IP Geolocation",
            "📱 Telefon Numarası Analizi", 
            "👤 Sosyal Medya OSINT",
            "🎨 Cyan/Turquoise Renk Teması",
            "⚡ Animasyonlu Arayüz",
            "🔍 Gerçek Zamanlı Veri",
            "🏢 BAGLAN Tech Solutions",
            "👤 Created by @nerwa_degme"
        ]
        
        for feature in features:
            print(f"{C.GREEN_CYAN}{feature}")
            time.sleep(0.3)
        
        print(f"{C.CYAN_BRIGHT}{'═' * 50}")
        print(f"{C.TURQUOISE}🎯 Profesyonel OSINT aracı!")
        print(f"{C.GREEN}✅ Gerçek verilerle çalışır!")
        print(f"{C.YELLOW}⚠️  Sadece yasal amaçlarla kullanın!")
    
    def run_demo(self):
        """Demo çalıştır"""
        self.animate_logo()
        
        print(f"\n{C.TURQUOISE}{C.BOLD}🎬 BAGLAN GHOST TRACKER DEMO BAŞLIYOR...")
        time.sleep(2)
        
        self.demo_ip_tracker()
        self.demo_phone_tracker() 
        self.demo_username_tracker()
        self.demo_features()
        
        print(f"\n{C.GREEN}{C.BOLD}{'🎉' * 20}")
        print(f"🎊 DEMO TAMAMLANDI! 🎊")
        print(f"{'🎉' * 20}")
        
        print(f"\n{C.TURQUOISE}📋 Gerçek kullanım için:")
        print(f"{C.CYAN_BRIGHT}   python BAGLAN_GhostTracker.py")
        print(f"\n{C.GREEN_CYAN}🙏 Teşekkürler! - {self.author}")
        print(f"{C.TURQUOISE}🏢 {self.company}")
        print(f"{C.TURQUOISE}{'═' * 50}{C.RESET}")

if __name__ == "__main__":
    demo = BaglanGhostDemo()
    demo.run_demo()
