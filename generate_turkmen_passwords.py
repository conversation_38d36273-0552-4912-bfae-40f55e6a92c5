#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import random
import string

def generate_turkmen_passwords():
    """10 milyon Türkmen şifresi oluştur"""
    
    # Türkmen isimleri
    turkmen_names = [
        "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Döwran", "<PERSON>rdan", 
        "Oguljan", "Bahar", "Serdar", "Jennet", "Gurban", "Mahr<PERSON>", "Wepa", 
        "Ayna", "Döwletmyrat", "Oguzhan", "Gulzada", "Merjen", "Batyr", 
        "Amanmyrat", "Gurbanguly", "Saparmyrat", "Berdimuhamedow", "Niyazow",
        "Gorogly", "Magtymguly", "Mollanepes", "Kemine", "Zelili", "Andalyp",
        "Makhtumkuli", "Pyragy", "<PERSON>ydi", "<PERSON><PERSON><PERSON>", "<PERSON>ur<PERSON>hammet", "<PERSON><PERSON><PERSON>",
        "<PERSON>vlet<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>hammet", "Rejep<PERSON>", "Annamy<PERSON>",
        "Charymyrat", "Dovletgeldi", "Gurbangeld", "Hojaniyaz", "Muhammetgeldi"
    ]
    
    # Türkmen şehirleri
    cities = [
        "Aşgabat", "Mary", "Türkmenbaşy", "Balkanabat", "Daşoguz", "Türkmenabat",
        "Tejen", "Serdar", "Bereket", "Gumdag", "Garabogaz", "Esenguly",
        "Magdanly", "Gyzylarbat", "Baharly", "Akdepe", "Köneürgench", "Gazojak"
    ]
    
    # Türkmen kelimeleri
    turkmen_words = [
        "oglan", "gyz", "ene", "ata", "mama", "dede", "eje", "agtyq", "doganyq",
        "garyndash", "ussa", "mugallym", "lukman", "türkmen", "watan", "bayrak",
        "döwlet", "halk", "millet", "dil", "medeniyet", "tarih", "gelecek",
        "bahar", "tomus", "güz", "gyş", "gün", "aý", "ýyl", "sagat", "minut"
    ]
    
    # Yaygın şifreler
    common_patterns = [
        "123456", "password", "12345678", "qwerty", "123456789", "12345",
        "1234", "111111", "1234567", "dragon", "123123", "baseball", "abc123",
        "football", "monkey", "letmein", "shadow", "master", "666666", "qwertyuiop"
    ]
    
    passwords = set()  # Tekrarları önlemek için set kullan
    
    print("Türkmen şifreleri oluşturuluyor...")
    
    # 1. Türkmen isimleri + sayılar
    for name in turkmen_names:
        for i in range(1000):
            passwords.add(f"{name}{i}")
            passwords.add(f"{name.lower()}{i}")
            passwords.add(f"{name.upper()}{i}")
            
    # 2. Şehirler + sayılar
    for city in cities:
        for i in range(1000):
            passwords.add(f"{city}{i}")
            passwords.add(f"{city.lower()}{i}")
            passwords.add(f"{city.upper()}{i}")
            
    # 3. Türkmen kelimeleri + sayılar
    for word in turkmen_words:
        for i in range(500):
            passwords.add(f"{word}{i}")
            passwords.add(f"{word.lower()}{i}")
            passwords.add(f"{word.upper()}{i}")
            
    # 4. Telefon numaraları
    for i in range(100000):
        phone = f"+993{random.randint(61000000, 69999999)}"
        passwords.add(phone)
        passwords.add(phone.replace("+", ""))
        
    # 5. Yaygın şifreler
    for pattern in common_patterns:
        for i in range(1000):
            passwords.add(f"{pattern}{i}")
            passwords.add(f"{i}{pattern}")
            
    # 6. Kombinasyonlar
    for name in turkmen_names[:20]:
        for city in cities[:10]:
            for i in range(100):
                passwords.add(f"{name}{city}{i}")
                passwords.add(f"{name.lower()}{city.lower()}{i}")
                
    # 7. Rastgele kombinasyonlar
    chars = string.ascii_letters + string.digits
    for _ in range(1000000):
        length = random.randint(6, 12)
        password = ''.join(random.choice(chars) for _ in range(length))
        passwords.add(password)
        
    # 8. Türkmenistan özel kombinasyonlar
    special_words = ["turkmenistan", "ashgabat", "manat", "altyn", "ak", "gara"]
    for word in special_words:
        for i in range(10000):
            passwords.add(f"{word}{i}")
            passwords.add(f"{word.upper()}{i}")
            passwords.add(f"{word.capitalize()}{i}")
            passwords.add(f"{i}{word}")
            
    # 9. Yıl kombinasyonları
    for year in range(1990, 2025):
        for name in turkmen_names[:30]:
            passwords.add(f"{name}{year}")
            passwords.add(f"{name.lower()}{year}")
            
    # 10. Daha fazla rastgele şifre ekle
    while len(passwords) < 10000000:
        # Rastgele uzunlukta şifre
        length = random.randint(4, 16)
        password = ''.join(random.choice(chars) for _ in range(length))
        passwords.add(password)
        
        # İlerleme göster
        if len(passwords) % 100000 == 0:
            print(f"Oluşturulan şifre sayısı: {len(passwords):,}")
    
    return list(passwords)

def save_passwords_to_file(passwords, filename):
    """Şifreleri dosyaya kaydet"""
    print(f"\n{len(passwords):,} şifre {filename} dosyasına kaydediliyor...")
    
    with open(filename, 'w', encoding='utf-8') as f:
        for i, password in enumerate(passwords, 1):
            f.write(f"{password}\n")
            
            # İlerleme göster
            if i % 100000 == 0:
                print(f"Kaydedilen: {i:,}/{len(passwords):,}")
    
    print(f"✅ {filename} dosyası oluşturuldu!")

if __name__ == "__main__":
    print("🇹🇲 Türkmen Şifre Listesi Oluşturucu")
    print("=" * 50)
    
    # Şifreleri oluştur
    passwords = generate_turkmen_passwords()
    
    # Dosyaya kaydet
    save_passwords_to_file(passwords, "turkmen_10million_passwords.txt")
    
    print(f"\n🎉 Toplam {len(passwords):,} Türkmen şifresi oluşturuldu!")
    print("📁 Dosya: turkmen_10million_passwords.txt")
