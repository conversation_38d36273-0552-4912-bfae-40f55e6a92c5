#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗
██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║
██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║
██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║
██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║

BAGLAN IMAGE LOCATION FINDER v1.0
Created by: @nerwa_degme
Company: BAGLAN Tech Solutions
"""

import os
import time
import sys
from PIL import Image
from PIL.ExifTags import TAGS, GPSTAGS
import requests
import json

# BAGLAN Color Scheme
class Colors:
    CYAN_BRIGHT = '\033[38;2;0;255;255m'     # Parlak cyan
    TURQUOISE = '\033[38;2;0;255;204m'       # Turkuaz
    GREEN_CYAN = '\033[38;2;0;255;179m'      # Yeşilimsi cyan
    GREEN = '\033[38;2;100;255;100m'         # Yeşil
    RED = '\033[38;2;255;100;100m'           # Kırmızı
    YELLOW = '\033[38;2;255;255;100m'        # Sarı
    WHITE = '\033[38;2;255;255;255m'         # Beyaz
    BOLD = '\033[1m'
    RESET = '\033[0m'

C = Colors()

class BaglanImageLocationFinder:
    def __init__(self):
        self.version = "1.0"
        self.author = "@nerwa_degme"
        self.company = "BAGLAN Tech Solutions"
    
    def clear_screen(self):
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def animate_logo(self):
        """Süper animasyonlu logo"""
        self.clear_screen()
        
        logo_frames = [
            f"""{C.CYAN_BRIGHT}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}""",
            
            f"""{C.TURQUOISE}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ║        ██╗███╗   ███╗ █████╗  ██████╗ ███████╗          ║
    ║        ██║████╗ ████║██╔══██╗██╔════╝ ██╔════╝          ║
    ║        ██║██╔████╔██║███████║██║  ███╗█████╗            ║
    ║        ██║██║╚██╔╝██║██╔══██║██║   ██║██╔══╝            ║
    ║        ██║██║ ╚═╝ ██║██║  ██║╚██████╔╝███████╗          ║
    ║        ╚═╝╚═╝     ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝          ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}""",
            
            f"""{C.GREEN_CYAN}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ║        ██╗███╗   ███╗ █████╗  ██████╗ ███████╗          ║
    ║        ██║████╗ ████║██╔══██╗██╔════╝ ██╔════╝          ║
    ║        ██║██╔████╔██║███████║██║  ███╗█████╗            ║
    ║        ██║██║╚██╔╝██║██╔══██║██║   ██║██╔══╝            ║
    ║        ██║██║ ╚═╝ ██║██║  ██║╚██████╔╝███████╗          ║
    ║        ╚═╝╚═╝     ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝          ║
    ║                                                          ║
    ║      ██╗      ██████╗  ██████╗ █████╗ ████████╗██╗ ██████╗ ███╗   ██╗ ║
    ║      ██║     ██╔═══██╗██╔════╝██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║ ║
    ║      ██║     ██║   ██║██║     ███████║   ██║   ██║██║   ██║██╔██╗ ██║ ║
    ║      ██║     ██║   ██║██║     ██╔══██║   ██║   ██║██║   ██║██║╚██╗██║ ║
    ║      ███████╗╚██████╔╝╚██████╗██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║ ║
    ║      ╚══════╝ ╚═════╝  ╚═════╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝ ║
    ║                                                          ║
    ║              {C.CYAN_BRIGHT}📸 FIND LOCATION FROM IMAGES 📍{C.GREEN_CYAN}              ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}"""
        ]
        
        for frame in logo_frames:
            self.clear_screen()
            print(frame)
            time.sleep(1.0)
        
        # Animasyonlu başlık
        print(f"\n{C.TURQUOISE}{C.BOLD}{'═' * 62}")
        title = f"🚀 BAGLAN IMAGE LOCATION FINDER v{self.version} 🚀"
        for i, char in enumerate(title):
            print(f"\r{' ' * ((62 - len(title)) // 2)}{title[:i+1]}", end='', flush=True)
            time.sleep(0.05)
        
        print(f"\n{C.TURQUOISE}{'═' * 62}")
        print(f"{C.GREEN_CYAN}              📸 GPS Koordinat Çıkarıcı 📍")
        print(f"{C.CYAN_BRIGHT}               👤 Created by {self.author}")
        print(f"{C.TURQUOISE}              🏢 {self.company}")
        print(f"{C.TURQUOISE}{'═' * 62}{C.RESET}")
        time.sleep(1)
    
    def show_loading(self, text="İşleniyor", duration=15):
        """Animasyonlu yükleme"""
        spinners = ['📸', '🔍', '🗺️', '📍']
        
        for i in range(duration):
            char = spinners[i % len(spinners)]
            progress = int((i / duration) * 20)
            bar = '█' * progress + '░' * (20 - progress)
            percentage = int((i / duration) * 100)
            
            print(f"\r{C.CYAN_BRIGHT}{char} {text}... [{C.TURQUOISE}{bar}{C.CYAN_BRIGHT}] {percentage}%", end='', flush=True)
            time.sleep(0.1)
        
        print(f"\r{C.GREEN}✅ {text} tamamlandı! {' ' * 30}{C.RESET}")
        time.sleep(0.5)
    
    def get_gps_coordinates(self, image_path):
        """Görüntüden GPS koordinatlarını çıkar"""
        try:
            # Görüntüyü aç
            image = Image.open(image_path)
            
            # EXIF verilerini al
            exif_data = image._getexif()
            
            if exif_data is None:
                return None, "EXIF verisi bulunamadı"
            
            # GPS verilerini ara
            gps_info = {}
            for tag, value in exif_data.items():
                tag_name = TAGS.get(tag, tag)
                if tag_name == "GPSInfo":
                    for gps_tag in value:
                        gps_tag_name = GPSTAGS.get(gps_tag, gps_tag)
                        gps_info[gps_tag_name] = value[gps_tag]
            
            if not gps_info:
                return None, "GPS verisi bulunamadı"
            
            # Koordinatları dönüştür
            lat = self.convert_to_degrees(gps_info.get('GPSLatitude'))
            lat_ref = gps_info.get('GPSLatitudeRef')
            lon = self.convert_to_degrees(gps_info.get('GPSLongitude'))
            lon_ref = gps_info.get('GPSLongitudeRef')
            
            if lat and lon:
                # Güney ve Batı için negatif yap
                if lat_ref == 'S':
                    lat = -lat
                if lon_ref == 'W':
                    lon = -lon
                
                return (lat, lon), "Başarılı"
            else:
                return None, "GPS koordinatları çıkarılamadı"
                
        except Exception as e:
            return None, f"Hata: {str(e)}"
    
    def convert_to_degrees(self, value):
        """GPS koordinatlarını derece formatına çevir"""
        if not value:
            return None
        
        try:
            d, m, s = value
            return float(d) + float(m)/60 + float(s)/3600
        except:
            return None
    
    def get_location_info(self, lat, lon):
        """Koordinatlardan konum bilgisi al"""
        try:
            # Reverse geocoding API
            url = f"https://api.bigdatacloud.net/data/reverse-geocode-client?latitude={lat}&longitude={lon}&localityLanguage=tr"
            response = requests.get(url, timeout=10)
            data = response.json()
            
            return {
                'city': data.get('city', 'Bilinmiyor'),
                'region': data.get('principalSubdivision', 'Bilinmiyor'),
                'country': data.get('countryName', 'Bilinmiyor'),
                'address': data.get('locality', 'Bilinmiyor')
            }
        except:
            return {
                'city': 'API Hatası',
                'region': 'API Hatası', 
                'country': 'API Hatası',
                'address': 'API Hatası'
            }
    
    def analyze_image(self):
        """Görüntü analiz et"""
        self.clear_screen()
        print(f"{C.TURQUOISE}{C.BOLD}")
        print("╔══════════════════════════════════════════════════════════╗")
        print("║                   📸 IMAGE LOCATION FINDER                ║")
        print("╚══════════════════════════════════════════════════════════╝")
        print(f"{C.RESET}")
        
        # Dosya yolu al
        image_path = input(f"{C.CYAN_BRIGHT}📁 Görüntü dosyasının yolunu girin: {C.WHITE}")
        
        if not image_path:
            print(f"{C.RED}❌ Dosya yolu boş olamaz!{C.RESET}")
            return
        
        if not os.path.exists(image_path):
            print(f"{C.RED}❌ Dosya bulunamadı: {image_path}{C.RESET}")
            return
        
        # Dosya kontrolü
        valid_extensions = ['.jpg', '.jpeg', '.png', '.tiff', '.tif']
        if not any(image_path.lower().endswith(ext) for ext in valid_extensions):
            print(f"{C.RED}❌ Desteklenmeyen dosya formatı! Desteklenen: {', '.join(valid_extensions)}{C.RESET}")
            return
        
        self.show_loading("Görüntü analiz ediliyor")
        
        # GPS koordinatlarını çıkar
        coordinates, message = self.get_gps_coordinates(image_path)
        
        print(f"\n{C.TURQUOISE}{C.BOLD}{'═' * 60}")
        print(f"{C.TURQUOISE}📸 GÖRÜNTÜ KONUM ANALİZİ")
        print(f"{C.TURQUOISE}{'═' * 60}")
        
        print(f"{C.CYAN_BRIGHT}📁 Dosya: {C.WHITE}{os.path.basename(image_path)}")
        print(f"{C.CYAN_BRIGHT}📂 Yol: {C.WHITE}{image_path}")
        
        if coordinates:
            lat, lon = coordinates
            print(f"{C.GREEN}✅ GPS Verisi: {C.WHITE}Bulundu!")
            print(f"{C.CYAN_BRIGHT}📍 Enlem (Latitude): {C.WHITE}{lat:.6f}")
            print(f"{C.CYAN_BRIGHT}📍 Boylam (Longitude): {C.WHITE}{lon:.6f}")
            
            # Konum bilgilerini al
            self.show_loading("Konum bilgileri alınıyor", 10)
            location_info = self.get_location_info(lat, lon)
            
            print(f"\n{C.TURQUOISE}🗺️  KONUM BİLGİLERİ:")
            print(f"{C.CYAN_BRIGHT}🏙️  Şehir: {C.WHITE}{location_info['city']}")
            print(f"{C.CYAN_BRIGHT}🗺️  Bölge: {C.WHITE}{location_info['region']}")
            print(f"{C.CYAN_BRIGHT}🏳️  Ülke: {C.WHITE}{location_info['country']}")
            print(f"{C.CYAN_BRIGHT}📍 Adres: {C.WHITE}{location_info['address']}")
            
            # Harita linkleri
            print(f"\n{C.TURQUOISE}🔗 HARİTA LİNKLERİ:")
            google_maps = f"https://www.google.com/maps/@{lat},{lon},15z"
            openstreetmap = f"https://www.openstreetmap.org/?mlat={lat}&mlon={lon}&zoom=15"
            
            print(f"{C.GREEN}🗺️  Google Maps: {C.WHITE}{google_maps}")
            print(f"{C.GREEN}🗺️  OpenStreetMap: {C.WHITE}{openstreetmap}")
            
        else:
            print(f"{C.RED}❌ GPS Verisi: {C.WHITE}Bulunamadı")
            print(f"{C.YELLOW}💡 Sebep: {C.WHITE}{message}")
            print(f"{C.YELLOW}💡 Not: Görüntü GPS verisi içermiyor olabilir")
        
        print(f"{C.TURQUOISE}{'═' * 60}{C.RESET}")
        input(f"\n{C.TURQUOISE}📱 Devam etmek için Enter'a basın...{C.RESET}")
    
    def show_menu(self):
        """Ana menü"""
        print(f"\n{C.TURQUOISE}{C.BOLD}🎯 BAGLAN IMAGE LOCATION MENÜ")
        print(f"{C.CYAN_BRIGHT}{'─' * 40}")
        print(f"{C.GREEN_CYAN}[ 1 ] 📸 Görüntüden Konum Bul")
        print(f"{C.GREEN_CYAN}[ 2 ] 📁 Toplu Görüntü Analizi")
        print(f"{C.GREEN_CYAN}[ 3 ] ℹ️  Hakkında")
        print(f"{C.RED}[ 0 ] ❌ Çıkış")
        print(f"{C.CYAN_BRIGHT}{'─' * 40}")
        
        choice = input(f"{C.TURQUOISE}🎯 Seçiminizi yapın: {C.WHITE}")
        return choice
    
    def show_about(self):
        """Hakkında bilgileri"""
        self.clear_screen()
        print(f"{C.TURQUOISE}{C.BOLD}")
        print("╔══════════════════════════════════════════════════════════╗")
        print("║                        ℹ️  HAKKINDA                        ║")
        print("╚══════════════════════════════════════════════════════════╝")
        print(f"{C.RESET}")
        
        info = [
            "📸 BAGLAN Image Location Finder",
            f"🔢 Sürüm: v{self.version}",
            f"👤 Geliştirici: {self.author}",
            f"🏢 Şirket: {self.company}",
            "",
            "🎯 ÖZELLİKLER:",
            "• 📍 EXIF GPS verisi çıkarma",
            "• 🗺️  Reverse geocoding",
            "• 🔗 Google Maps entegrasyonu",
            "• 📱 Kullanıcı dostu arayüz",
            "",
            "📋 DESTEKLENEN FORMATLAR:",
            "• JPG/JPEG",
            "• PNG (EXIF destekli)",
            "• TIFF/TIF",
            "",
            "⚠️  NOT:",
            "Sadece GPS verisi içeren görüntüler",
            "için konum bilgisi alınabilir."
        ]
        
        for line in info:
            print(f"{C.CYAN_BRIGHT}{line}")
            time.sleep(0.1)
        
        input(f"\n{C.TURQUOISE}📱 Devam etmek için Enter'a basın...{C.RESET}")
    
    def run(self):
        """Ana program"""
        self.animate_logo()
        
        while True:
            try:
                choice = self.show_menu()
                
                if choice == '1':
                    self.analyze_image()
                elif choice == '2':
                    print(f"{C.YELLOW}🚧 Bu özellik yakında eklenecek!{C.RESET}")
                    time.sleep(2)
                elif choice == '3':
                    self.show_about()
                elif choice == '0':
                    print(f"{C.TURQUOISE}👋 BAGLAN Image Location Finder'dan çıkılıyor...")
                    print(f"{C.CYAN_BRIGHT}🙏 Teşekkürler! - {self.author}{C.RESET}")
                    break
                else:
                    print(f"{C.RED}❌ Geçersiz seçim!{C.RESET}")
                    time.sleep(1)
                    
            except KeyboardInterrupt:
                print(f"\n{C.RED}❌ Program durduruldu.{C.RESET}")
                break
            except Exception as e:
                print(f"{C.RED}❌ Hata: {e}{C.RESET}")
                time.sleep(2)

if __name__ == "__main__":
    try:
        finder = BaglanImageLocationFinder()
        finder.run()
    except ImportError as e:
        print("❌ Gerekli kütüphaneler eksik!")
        print("📦 Yüklemek için: pip install Pillow requests")
        print(f"Hata: {e}")
    except Exception as e:
        print(f"❌ Beklenmeyen hata: {e}")
