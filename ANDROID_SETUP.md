# 🤖 BAGLAN WiFi Cracker - Android Kurulum Rehberi

## 📱 Android Desteği

BAGLAN WiFi Cracker artık Android cihazlarda da çalışır! Gerçek WiFi ağlarını tarar ve şifreleri test eder.

## 🔧 Gereksinimler

### 1. **Termux Uygulaması**
```bash
# Google Play Store'dan Termux indir
# Veya F-Droid'den indir (önerilen)
```

### 2. **Python ve Kütüphaneler**
```bash
# Termux'ta Python yükle
pkg update && pkg upgrade
pkg install python

# Gerekli kütüphaneleri yükle
pip install colorama
```

### 3. **WiFi Araçları (Opsiyonel)**
```bash
# Wireless araçları yükle
pkg install iw
pkg install wireless-tools

# Root erişimi varsa
pkg install wpa-supplicant
```

### 4. **Termux API (Opsiyonel)**
```bash
# Termux API yükle
pkg install termux-api

# Google Play'den Termux:API uygulamasını indir
```

## 🚀 Kurulum Adımları

### Adım 1: Termux Kurulumu
1. **F-Droid'den Termux indir** (önerilen)
2. Termux'u aç ve güncellemeleri yap:
```bash
pkg update && pkg upgrade
```

### Adım 2: Python Kurulumu
```bash
# Python yükle
pkg install python

# Pip güncellemesi
pip install --upgrade pip
```

### Adım 3: BAGLAN WiFi Cracker Kurulumu
```bash
# Gerekli kütüphaneyi yükle
pip install colorama

# Dosyaları indir (GitHub'dan veya manuel)
# wifi_cracker.py dosyasını Termux'a kopyala
```

### Adım 4: İzinler ve Araçlar
```bash
# WiFi araçlarını yükle
pkg install iw wireless-tools

# Termux API (opsiyonel)
pkg install termux-api
```

## 🔓 Root Erişimi (Opsiyonel)

Root erişimi ile daha gelişmiş özellikler:

```bash
# Root kontrolü
su -c "echo 'Root erişimi var!'"

# Root ile ek araçlar
pkg install wpa-supplicant
pkg install aircrack-ng  # Eğer mevcut ise
```

## 📱 Çalıştırma

### Temel Kullanım
```bash
# Ana program
python wifi_cracker.py

# Demo
python demo_baglan_wifi.py
```

### Android Özel Özellikler
- ✅ Otomatik Android algılama
- ✅ Termux API desteği
- ✅ Root erişimi kontrolü
- ✅ Multiple WiFi tarama yöntemi
- ✅ Android özel bağlantı testleri

## 🛠️ Sorun Giderme

### Problem: "iw komutu bulunamadı"
```bash
pkg install iw
```

### Problem: "Root erişimi yok"
```bash
# Root gerekmez, sınırlı özelliklerle çalışır
# Root için cihazınızı root'layın
```

### Problem: "WiFi ağları bulunamadı"
```bash
# Manuel ağ adı girebilirsiniz
# Veya farklı tarama komutları deneyin
```

### Problem: "İzin hatası"
```bash
# Termux'ta storage izni ver
termux-setup-storage

# WiFi izinleri kontrol et
```

## 📋 Desteklenen Android Sürümleri

- ✅ **Android 7.0+** (API 24+)
- ✅ **Termux** ortamında
- ✅ **Root'lu cihazlar** (gelişmiş özellikler)
- ✅ **Root'suz cihazlar** (temel özellikler)

## 🔍 WiFi Tarama Yöntemleri

### 1. **iw komutu**
```bash
iw dev wlan0 scan | grep "SSID:"
```

### 2. **iwlist komutu**
```bash
iwlist wlan0 scan | grep "ESSID:"
```

### 3. **Termux API**
```bash
termux-wifi-scaninfo
```

### 4. **Root ile gelişmiş tarama**
```bash
su -c "iw dev wlan0 scan | grep SSID"
```

## ⚠️ Önemli Notlar

1. **Yasal Uyarı**: Sadece kendi WiFi ağlarınızı test edin
2. **Root Gerekliliği**: Bazı özellikler root gerektirir
3. **Pil Tüketimi**: Yoğun tarama pil tüketir
4. **İzinler**: WiFi ve ağ izinleri gerekli

## 🎯 Android Özel Komutlar

```bash
# Platform kontrolü
python -c "from wifi_cracker import detect_platform; print(detect_platform())"

# Android test
python -c "
import subprocess
result = subprocess.run('getprop ro.build.version.release', shell=True, capture_output=True, text=True)
print('Android Sürümü:', result.stdout.strip())
"
```

## 📞 Destek

Android ile ilgili sorunlar için:
- Termux dokümantasyonunu kontrol edin
- F-Droid Termux sürümünü kullanın
- Root erişimi için XDA forumlarına bakın

---

**🚀 BAGLAN WiFi Cracker - Android'de de güçlü! 🤖**
