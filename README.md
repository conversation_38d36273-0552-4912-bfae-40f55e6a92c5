# 🚀 BAGLAN WiFi Password Cracker v2.0

## 🎯 Özellikler

### ✨ **Yeni Android Desteği!**
- 🤖 **Otomatik platform algılama** (Windows, Linux, Mac, **Android**)
- 📱 **Termux desteği** - Android'de native çalışır
- 🔓 **Root erişimi** ile gelişmiş özellikler
- 📡 **Multiple WiFi tarama** yöntemleri

### 🎨 **Görsel Özellikler**
- 🌈 **Cyan/Turquoise gradient** renk teması (görüntünüzdeki gibi)
- ⚡ **Animasyonlu logo** ve başlık
- 📊 **Progress bar'lar** ve loading animasyonları
- 🎭 **Spinner animasyonları** şifre testi sırasında
- 🎉 **Başarı animasyonları** şifre bulunduğunda

### 🔧 **Teknik Özellikler**
- 🚀 **Ultra hızlı paralel** şifre testi (3 thread)
- 🎯 **Gerçek WiFi bağlantı** testleri (simülasyon değil!)
- 📁 **4 farklı şifre dosyası** desteği
- 🔄 **Otomatik temizlik** sistemi
- 📊 **Detaylı istatistikler**

## 🖥️ Platform Desteği

| Platform | Durum | Özellikler |
|----------|-------|------------|
| 🪟 **Windows** | ✅ Tam Destek | netsh, gerçek bağlantı testi |
| 🐧 **Linux** | ✅ Tam Destek | nmcli, iwconfig desteği |
| 🍎 **macOS** | ✅ Tam Destek | networksetup desteği |
| 🤖 **Android** | ✅ **YENİ!** | Termux, iw, wpa_supplicant |

## 📦 Kurulum

### Windows/Linux/Mac:
```bash
pip install colorama
```

### Android (Termux):
```bash
# Termux yükle (F-Droid önerilen)
pkg update && pkg upgrade
pkg install python
pip install colorama

# WiFi araçları (opsiyonel)
pkg install iw wireless-tools
```

## 🚀 Kullanım

### Ana Program:
```bash
python wifi_cracker.py
```

### Demo:
```bash
python demo_baglan_wifi.py
```

### Android Test:
```bash
python test_android.py
```

## 🎨 Görsel Örnekler

### Logo Animasyonu:
```
╔══════════════════════════════════════════════════════════╗
║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
║        ██╗    ██╗██╗███████╗██╗    ██╗██╗███████╗██╗     ║
║        ██║ █╗ ██║██║█████╗  ██║ █╗ ██║██║█████╗  ██║     ║
║                  🔐 PASSWORD CRACKER 🔐                  ║
╚══════════════════════════════════════════════════════════╝
```

### Progress Bar:
```
🔄 [████████░░] 80% - Test: turkmen2024
⠋ [  47] Test: demo_password_47
```

### Başarı Ekranı:
```
🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉
████████████████████████████████████████████████████████████
█         🎯 BAGLAN WiFi CRACKER - ŞİFRE BULUNDU! 🎯         █
████████████████████████████████████████████████████████████
🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉🎉
```

## 🤖 Android Özel Özellikler

### Otomatik Algılama:
- ✅ `/system/build.prop` kontrolü
- ✅ `getprop` komutu testi
- ✅ Termux ortamı algılama
- ✅ Root erişimi kontrolü

### WiFi Tarama Yöntemleri:
```bash
# 1. iw komutu
iw dev wlan0 scan | grep "SSID:"

# 2. iwlist komutu  
iwlist wlan0 scan | grep "ESSID:"

# 3. Termux API
termux-wifi-scaninfo

# 4. Root ile gelişmiş
su -c "iw dev wlan0 scan | grep SSID"
```

### Bağlantı Test Yöntemleri:
- 🔧 **wpa_supplicant** ile bağlantı
- ⚡ **iw/iwconfig** komutları
- 📱 **Termux API** desteği
- 🔓 **Root** ile gelişmiş testler

## 📁 Dosya Yapısı

```
📂 BAGLAN WiFi Cracker/
├── 📄 wifi_cracker.py          # Ana program
├── 📄 demo_baglan_wifi.py      # Demo sürümü
├── 📄 test_android.py          # Android test
├── 📄 ANDROID_SETUP.md         # Android kurulum rehberi
├── 📄 README.md                # Bu dosya
├── 📁 10millionPasswords       # Şifre dosyası 1
├── 📁 10millionPasswords2      # Şifre dosyası 2
├── 📁 turkmen_10million_passwords.txt  # Turkmen şifreler
└── 📁 turkmen_passwords.txt    # Özel Turkmen şifreler
```

## ⚠️ Yasal Uyarı

Bu araç **sadece eğitim amaçlı** ve **kendi WiFi ağlarınızı test etmek** için tasarlanmıştır. 

- ✅ Kendi WiFi ağınızı test edin
- ✅ İzin aldığınız ağları test edin
- ❌ Başkalarının ağlarını test etmeyin
- ❌ Yasadışı amaçlarla kullanmayın

## 🔧 Gereksinimler

### Minimum:
- Python 3.6+
- colorama kütüphanesi

### Android için:
- Termux uygulaması
- iw/iwconfig (opsiyonel)
- Root erişimi (gelişmiş özellikler için)

## 📊 Performans

- ⚡ **3 paralel thread** ile hızlandırılmış test
- 🎯 **50+ şifre/saniye** test hızı
- 📁 **50,000+ şifre** dosya başına
- 🔄 **Otomatik temizlik** sistemi

## 🎉 Yenilikler v2.0

- 🤖 **Android desteği** eklendi
- 🎨 **Görüntünüzdeki renk teması** uygulandı
- ⚡ **Gelişmiş animasyonlar**
- 📱 **Termux optimizasyonu**
- 🔓 **Root erişimi** desteği
- 📊 **Detaylı platform bilgileri**

---

**🚀 BAGLAN WiFi Password Cracker - Artık Android'de de güçlü! 🤖**

*Görüntünüzdeki cyan/turquoise gradient teması ile tasarlandı* ✨
