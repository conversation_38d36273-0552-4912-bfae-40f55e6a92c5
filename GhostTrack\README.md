# GhostTrack
Useful tool to track location or mobile number, so this tool can be called osint or also information gathering

<img src="https://github.com/HunxByts/GhostTrack/blob/main/asset/bn.png"/>

New update :
```Version 2.2```

### Instalation on Linux (deb)
```
sudo apt-get install git
sudo apt-get install python3
```

### Instalation on Termux
```
pkg install git
pkg install python3
```

### Usage Tool
```
git clone https://github.com/HunxByts/GhostTrack.git
cd GhostTrack
pip3 install -r requirements.txt
python3 GhostTR.py
```

Display on the menu ```IP Tracker```

<img src="https://github.com/HunxByts/GhostTrack/blob/main/asset/ip.png " />

on the IP Track menu, you can combo with the seeker tool to get the target IP
<details>
<summary>:zap: Install Seeker :</summary>
- <strong><a href="https://github.com/thewhiteh4t/seeker">Get Seeker</a></strong>
</details>

Display on the menu ```Phone Tracker```

<img src="https://github.com/HunxByts/GhostTrack/blob/main/asset/phone.png" />

on this menu you can search for information from the target phone number

Display on the menu ```Username Tracker```

<img src="https://github.com/HunxByts/GhostTrack/blob/main/asset/User.png"/>
on this menu you can search for information from the target username on social media

<details>
<summary>:zap: Author :</summary>
- <strong><a href="https://github.com/HunxByts">HunxByts</a></strong>
</details>
