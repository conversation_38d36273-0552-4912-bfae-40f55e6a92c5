#!/usr/bin/python

# Kullanıcıdan kırılacak 5 haneli şifreyi al
sifre = input("Kırılacak 5 haneli şifreyi giriniz: ")

# Başlangıç şifresi ve adım sayısı
deneme_sifresi = "00000"  # Başlangıç olarak 00000
adim = 0

# Şifreyi doğru tahmin edene kadar döngü
while True:
    print("Şifre deneniyor...", deneme_sifresi)

    # Şifre doğruysa döngüden çık
    if deneme_sifresi == sifre:
        break
    else:
        # Bir sonraki şifreyi oluştur (basamak basamak artırma)
        deneme_sifresi = str(int(deneme_sifresi) + 1).zfill(5)  # 5 haneli format için zfill
        adim += 1

# Sonuçları yazdır
print("Şifreniz kırılmıştır:", sifre)
print("Toplam deneme sayısı:", adim)
