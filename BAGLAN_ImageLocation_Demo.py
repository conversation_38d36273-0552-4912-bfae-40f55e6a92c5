#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BAGLAN IMAGE LOCATION FINDER - Demo Sürümü
Created by: @nerwa_degme
Company: BAGLAN Tech Solutions
"""

import time
import os

# BAGLAN Color Scheme
class Colors:
    CYAN_BRIGHT = '\033[38;2;0;255;255m'     # Parlak cyan
    TURQUOISE = '\033[38;2;0;255;204m'       # Turkuaz
    GREEN_CYAN = '\033[38;2;0;255;179m'      # <PERSON><PERSON><PERSON>msi cyan
    GREEN = '\033[38;2;100;255;100m'         # Yeşil
    RED = '\033[38;2;255;100;100m'           # Kırmızı
    YELLOW = '\033[38;2;255;255;100m'        # Sarı
    WHITE = '\033[38;2;255;255;255m'         # Beyaz
    BOLD = '\033[1m'
    RESET = '\033[0m'

C = Colors()

class BaglanImageLocationDemo:
    def __init__(self):
        self.version = "1.0"
        self.author = "@nerwa_degme"
        self.company = "BAGLAN Tech Solutions"
    
    def clear_screen(self):
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def animate_logo(self):
        """Süper animasyonlu logo"""
        self.clear_screen()
        
        # Logo frame'leri
        logo_frames = [
            f"""{C.CYAN_BRIGHT}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}""",
            
            f"""{C.TURQUOISE}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ║        ██╗███╗   ███╗ █████╗  ██████╗ ███████╗          ║
    ║        ██║████╗ ████║██╔══██╗██╔════╝ ██╔════╝          ║
    ║        ██║██╔████╔██║███████║██║  ███╗█████╗            ║
    ║        ██║██║╚██╔╝██║██╔══██║██║   ██║██╔══╝            ║
    ║        ██║██║ ╚═╝ ██║██║  ██║╚██████╔╝███████╗          ║
    ║        ╚═╝╚═╝     ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝          ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}""",
            
            f"""{C.GREEN_CYAN}{C.BOLD}
    ╔══════════════════════════════════════════════════════════╗
    ║                                                          ║
    ║     ██████╗  █████╗  ██████╗ ██╗      █████╗ ███╗   ██╗ ║
    ║     ██╔══██╗██╔══██╗██╔════╝ ██║     ██╔══██╗████╗  ██║ ║
    ║     ██████╔╝███████║██║  ███╗██║     ███████║██╔██╗ ██║ ║
    ║     ██╔══██╗██╔══██║██║   ██║██║     ██╔══██║██║╚██╗██║ ║
    ║     ██████╔╝██║  ██║╚██████╔╝███████╗██║  ██║██║ ╚████║ ║
    ║     ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝ ║
    ║                                                          ║
    ║        ██╗███╗   ███╗ █████╗  ██████╗ ███████╗          ║
    ║        ██║████╗ ████║██╔══██╗██╔════╝ ██╔════╝          ║
    ║        ██║██╔████╔██║███████║██║  ███╗█████╗            ║
    ║        ██║██║╚██╔╝██║██╔══██║██║   ██║██╔══╝            ║
    ║        ██║██║ ╚═╝ ██║██║  ██║╚██████╔╝███████╗          ║
    ║        ╚═╝╚═╝     ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝          ║
    ║                                                          ║
    ║      ██╗      ██████╗  ██████╗ █████╗ ████████╗██╗ ██████╗ ███╗   ██╗ ║
    ║      ██║     ██╔═══██╗██╔════╝██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║ ║
    ║      ██║     ██║   ██║██║     ███████║   ██║   ██║██║   ██║██╔██╗ ██║ ║
    ║      ██║     ██║   ██║██║     ██╔══██║   ██║   ██║██║   ██║██║╚██╗██║ ║
    ║      ███████╗╚██████╔╝╚██████╗██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║ ║
    ║      ╚══════╝ ╚═════╝  ╚═════╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝ ║
    ║                                                          ║
    ║              {C.CYAN_BRIGHT}📸 FIND LOCATION FROM IMAGES 📍{C.GREEN_CYAN}              ║
    ║                                                          ║
    ╚══════════════════════════════════════════════════════════╝{C.RESET}"""
        ]
        
        for frame in logo_frames:
            self.clear_screen()
            print(frame)
            time.sleep(1.0)
        
        # Animasyonlu başlık
        print(f"\n{C.TURQUOISE}{C.BOLD}{'═' * 62}")
        title = f"🚀 BAGLAN IMAGE LOCATION FINDER v{self.version} 🚀"
        for i, char in enumerate(title):
            print(f"\r{' ' * ((62 - len(title)) // 2)}{title[:i+1]}", end='', flush=True)
            time.sleep(0.05)
        
        print(f"\n{C.TURQUOISE}{'═' * 62}")
        print(f"{C.GREEN_CYAN}              📸 GPS Koordinat Çıkarıcı 📍")
        print(f"{C.CYAN_BRIGHT}               👤 Created by {self.author}")
        print(f"{C.TURQUOISE}              🏢 {self.company}")
        print(f"{C.TURQUOISE}{'═' * 62}{C.RESET}")
        time.sleep(1)
    
    def demo_image_analysis(self):
        """Görüntü analizi demosu"""
        print(f"\n{C.TURQUOISE}{C.BOLD}📸 IMAGE LOCATION ANALYSIS DEMO")
        print(f"{C.CYAN_BRIGHT}{'═' * 50}")
        
        # Demo görüntüleri
        demo_images = [
            {
                'name': 'istanbul_photo.jpg',
                'location': 'İstanbul, Türkiye',
                'lat': 41.0082,
                'lon': 28.9784,
                'details': 'Sultanahmet Camii yakınları'
            },
            {
                'name': 'ankara_photo.jpg', 
                'location': 'Ankara, Türkiye',
                'lat': 39.9334,
                'lon': 32.8597,
                'details': 'Kızılay Meydanı'
            },
            {
                'name': 'izmir_photo.jpg',
                'location': 'İzmir, Türkiye', 
                'lat': 38.4192,
                'lon': 27.1287,
                'details': 'Kordon Boyu'
            }
        ]
        
        for i, img in enumerate(demo_images, 1):
            print(f"\n{C.GREEN_CYAN}📸 Demo Görüntü {i}: {C.WHITE}{img['name']}")
            
            # Animasyonlu analiz
            spinners = ['📸', '🔍', '🗺️', '📍']
            for j in range(12):
                char = spinners[j % len(spinners)]
                progress = int((j / 12) * 20)
                bar = '█' * progress + '░' * (20 - progress)
                percentage = int((j / 12) * 100)
                
                print(f"\r{C.CYAN_BRIGHT}{char} Görüntü analiz ediliyor... [{C.TURQUOISE}{bar}{C.CYAN_BRIGHT}] {percentage}%", end='', flush=True)
                time.sleep(0.2)
            
            print(f"\r{C.GREEN}✅ Görüntü analizi tamamlandı! {' ' * 30}")
            
            # Sonuçları göster
            print(f"{C.TURQUOISE}{'─' * 50}")
            print(f"{C.CYAN_BRIGHT}📍 Enlem (Latitude): {C.WHITE}{img['lat']:.6f}")
            print(f"{C.CYAN_BRIGHT}📍 Boylam (Longitude): {C.WHITE}{img['lon']:.6f}")
            print(f"{C.CYAN_BRIGHT}🏙️  Konum: {C.WHITE}{img['location']}")
            print(f"{C.CYAN_BRIGHT}📍 Detay: {C.WHITE}{img['details']}")
            
            # Harita linki
            google_maps = f"https://www.google.com/maps/@{img['lat']},{img['lon']},15z"
            print(f"{C.GREEN}🗺️  Google Maps: {C.WHITE}{google_maps}")
            
            print(f"{C.TURQUOISE}{'─' * 50}")
            time.sleep(1)
    
    def demo_features(self):
        """Özellikler demosu"""
        print(f"\n{C.TURQUOISE}{C.BOLD}🚀 BAGLAN IMAGE LOCATION FINDER ÖZELLİKLERİ")
        print(f"{C.CYAN_BRIGHT}{'═' * 55}")
        
        features = [
            "📸 EXIF GPS Verisi Çıkarma",
            "🗺️  Reverse Geocoding API",
            "📍 Hassas Koordinat Tespiti",
            "🔗 Google Maps Entegrasyonu",
            "🌐 OpenStreetMap Desteği",
            "📱 Kullanıcı Dostu Arayüz",
            "⚡ Hızlı İşlem Süresi",
            "🎨 Cyan/Turquoise Renk Teması",
            "🏢 BAGLAN Tech Solutions",
            "👤 Created by @nerwa_degme"
        ]
        
        for feature in features:
            print(f"{C.GREEN_CYAN}{feature}")
            time.sleep(0.3)
        
        print(f"\n{C.TURQUOISE}📋 DESTEKLENEN FORMATLAR:")
        formats = ["📄 JPEG/JPG", "📄 PNG (EXIF destekli)", "📄 TIFF/TIF"]
        
        for fmt in formats:
            print(f"{C.CYAN_BRIGHT}   {fmt}")
            time.sleep(0.2)
        
        print(f"\n{C.YELLOW}⚠️  ÖNEMLİ NOTLAR:")
        notes = [
            "• Sadece GPS verisi içeren görüntüler analiz edilebilir",
            "• Sosyal medya fotoğrafları genelde GPS verisi içermez",
            "• Telefon kamerası ile çekilen fotoğraflar ideal",
            "• Konum servisleri açık olmalı"
        ]
        
        for note in notes:
            print(f"{C.YELLOW}   {note}")
            time.sleep(0.2)
        
        print(f"{C.CYAN_BRIGHT}{'═' * 55}")
        print(f"{C.TURQUOISE}🎯 Profesyonel görüntü konum analizi!")
        print(f"{C.GREEN}✅ Gerçek GPS verisi ile çalışır!")
        print(f"{C.RED}⚠️  Gizlilik için dikkatli kullanın!")
    
    def demo_usage_guide(self):
        """Kullanım rehberi demosu"""
        print(f"\n{C.TURQUOISE}{C.BOLD}📖 KULLANIM REHBERİ")
        print(f"{C.CYAN_BRIGHT}{'═' * 40}")
        
        steps = [
            "1️⃣  Programı başlatın: python BAGLAN_ImageLocation.py",
            "2️⃣  Menüden '1 - Görüntüden Konum Bul' seçin",
            "3️⃣  Görüntü dosyasının tam yolunu girin",
            "4️⃣  GPS verisi varsa konum bilgileri gösterilir",
            "5️⃣  Google Maps linkine tıklayarak haritada görün"
        ]
        
        for step in steps:
            print(f"{C.GREEN_CYAN}{step}")
            time.sleep(0.5)
        
        print(f"\n{C.TURQUOISE}💡 İPUÇLARI:")
        tips = [
            "📱 Telefon fotoğrafları en iyi sonucu verir",
            "🔍 Dosya yolunu tam olarak yazın",
            "📂 Drag & drop desteklenmez, manuel yazın",
            "🗺️  Harita linklerini tarayıcıda açın"
        ]
        
        for tip in tips:
            print(f"{C.CYAN_BRIGHT}   {tip}")
            time.sleep(0.3)
    
    def run_demo(self):
        """Demo çalıştır"""
        self.animate_logo()
        
        print(f"\n{C.TURQUOISE}{C.BOLD}🎬 BAGLAN IMAGE LOCATION FINDER DEMO BAŞLIYOR...")
        time.sleep(2)
        
        self.demo_image_analysis()
        self.demo_features()
        self.demo_usage_guide()
        
        print(f"\n{C.GREEN}{C.BOLD}{'🎉' * 25}")
        print(f"🎊 DEMO TAMAMLANDI! 🎊")
        print(f"{'🎉' * 25}")
        
        print(f"\n{C.TURQUOISE}📋 Gerçek kullanım için:")
        print(f"{C.CYAN_BRIGHT}   python BAGLAN_ImageLocation.py")
        print(f"\n{C.GREEN_CYAN}🙏 Teşekkürler! - {self.author}")
        print(f"{C.TURQUOISE}🏢 {self.company}")
        print(f"{C.TURQUOISE}{'═' * 50}{C.RESET}")

if __name__ == "__main__":
    demo = BaglanImageLocationDemo()
    demo.run_demo()
